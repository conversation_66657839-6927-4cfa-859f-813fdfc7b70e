{% extends "base.html" %}

{% block title %}Tableau de Bord Admin{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-tachometer-alt text-primary me-2"></i>
                    Tableau de Bord Administrateur
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('admin.activity_logs') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i>Journaux d'Activité
                    </a>
                    <a href="{{ url_for('admin.backups') }}" class="btn btn-outline-success">
                        <i class="fas fa-database me-1"></i>Sauvegardes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Utilisateurs Actifs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ active_users }} / {{ total_users }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Sauvegardes Totales
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ total_backups }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-database fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Activités Aujourd'hui
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ recent_activities|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Système
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <span class="badge badge-success">En Ligne</span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-server fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Activities -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>Activités Récentes
                    </h6>
                    <a href="{{ url_for('admin.activity_logs') }}" class="btn btn-sm btn-outline-primary">
                        Voir Tout
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Utilisateur</th>
                                        <th>Action</th>
                                        <th>Description</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for activity in recent_activities %}
                                    <tr>
                                        <td>
                                            <strong>{{ activity.username }}</strong>
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ activity.action_color }}">
                                                <i class="{{ activity.action_icon }}"></i>
                                                {{ activity.action }}
                                            </span>
                                        </td>
                                        <td>
                                            <small>{{ activity.description or '-' }}</small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                {{ activity.timestamp.strftime('%d/%m/%Y %H:%M') }}
                                            </small>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune activité récente</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Backups -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-success">
                        <i class="fas fa-database me-2"></i>Sauvegardes Récentes
                    </h6>
                    <a href="{{ url_for('admin.backups') }}" class="btn btn-sm btn-outline-success">
                        Gérer
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_backups %}
                        {% for backup in recent_backups %}
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <i class="{{ backup.status_icon }}"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small font-weight-bold">
                                    {{ backup.filename }}
                                </div>
                                <div class="small text-muted">
                                    {{ backup.created_at.strftime('%d/%m/%Y %H:%M') }}
                                    <span class="badge badge-secondary">{{ backup.file_size_human }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-database fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune sauvegarde</p>
                            <a href="{{ url_for('admin.backups') }}" class="btn btn-sm btn-success">
                                Créer une Sauvegarde
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>Actions Rapides
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <form method="POST" action="{{ url_for('admin.create_backup') }}" class="d-inline">
                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-database me-2"></i>
                                    Créer Sauvegarde
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin.export_data') }}" class="btn btn-info btn-block">
                                <i class="fas fa-download me-2"></i>
                                Exporter Données
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin.import_data') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-upload me-2"></i>
                                Importer Données
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('admin.activity_logs') }}" class="btn btn-secondary btn-block">
                                <i class="fas fa-list me-2"></i>
                                Voir Journaux
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}
