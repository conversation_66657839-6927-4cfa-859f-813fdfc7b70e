{% extends 'base.html' %}

{% block title %}Informations de l'Entreprise - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-building me-2"></i>Informations de l'Entreprise
    </h1>
    <a href="{{ url_for('company.edit') }}" class="btn btn-primary">
        <i class="fas fa-edit me-1"></i>Modifier
    </a>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations générales</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    {% if company.logo %}
                    <img src="{{ url_for('static', filename=company.logo) }}" alt="{{ company.name }}" class="img-fluid" style="max-height: 150px;">
                    {% else %}
                    <div class="logo">
                        <div class="logo-icon"><span>M</span></div>
                        {{ company.name }}
                    </div>
                    {% endif %}
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Nom:</div>
                    <div class="col-md-8">{{ company.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Responsable:</div>
                    <div class="col-md-8">{{ company.manager or 'Non spécifié' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Téléphone:</div>
                    <div class="col-md-8">
                        {% if company.phone %}
                        <a href="tel:{{ company.phone }}">{{ company.phone }}</a>
                        {% else %}
                        Non spécifié
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Email:</div>
                    <div class="col-md-8">
                        {% if company.email %}
                        <a href="mailto:{{ company.email }}">{{ company.email }}</a>
                        {% else %}
                        Non spécifié
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Adresse:</div>
                    <div class="col-md-8">{{ company.address or 'Non spécifiée' }}</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations fiscales</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered">
                        <tbody>
                            <tr>
                                <th>IF (Identifiant Fiscal)</th>
                                <td>{{ company.tax_id or 'Non spécifié' }}</td>
                            </tr>
                            <tr>
                                <th>RC (Registre de Commerce)</th>
                                <td>{{ company.rc or 'Non spécifié' }}</td>
                            </tr>
                            <tr>
                                <th>Patente</th>
                                <td>{{ company.patente or 'Non spécifiée' }}</td>
                            </tr>
                            <tr>
                                <th>ICE</th>
                                <td>{{ company.ice or 'Non spécifié' }}</td>
                            </tr>
                            <tr>
                                <th>CNSS</th>
                                <td>{{ company.cnss or 'Non spécifiée' }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Pied de page personnalisé</h5>
            </div>
            <div class="card-body">
                {% if company.footer_text %}
                <div class="p-3 bg-light rounded">
                    {{ company.footer_text|nl2br }}
                </div>
                {% else %}
                <p class="text-muted">Aucun texte de pied de page défini.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Aperçu des documents</h5>
    </div>
    <div class="card-body">
        <p>Voici comment les informations de votre entreprise apparaîtront sur vos documents :</p>
        
        <div class="border p-3 mb-3">
            <div class="d-flex align-items-center mb-3">
                {% if company.logo %}
                <img src="{{ url_for('static', filename=company.logo) }}" alt="{{ company.name }}" class="me-3" style="max-height: 50px;">
                {% else %}
                <div class="logo" style="font-size: 24px;">
                    <div class="logo-icon" style="width: 40px; height: 40px;"><span>M</span></div>
                    {{ company.name }}
                </div>
                {% endif %}
            </div>
            
            <div class="row small">
                <div class="col-md-6">
                    {% if company.address %}
                    <p class="mb-1"><i class="fas fa-map-marker-alt me-1"></i> {{ company.address }}</p>
                    {% endif %}
                    {% if company.phone %}
                    <p class="mb-1"><i class="fas fa-phone me-1"></i> {{ company.phone }}</p>
                    {% endif %}
                    {% if company.email %}
                    <p class="mb-1"><i class="fas fa-envelope me-1"></i> {{ company.email }}</p>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    {% if company.tax_id %}
                    <p class="mb-1">IF: {{ company.tax_id }}</p>
                    {% endif %}
                    {% if company.rc %}
                    <p class="mb-1">RC: {{ company.rc }}</p>
                    {% endif %}
                    {% if company.ice %}
                    <p class="mb-1">ICE: {{ company.ice }}</p>
                    {% endif %}
                </div>
            </div>
            
            <hr>
            
            <div class="small text-center text-muted">
                {% if company.footer_text %}
                {{ company.footer_text|nl2br }}
                {% else %}
                SARL au capital de 2 110 000,00 dh - adresse : N° 19 , Amal 14 CYM Hay Elkhier Rabat /RC N° : 64133 - Patente N° 27395222 IF :3346362 - CNSS N° : 7304405 BANQUE POPULAIRE AGENCE RABAT IBNOU ROCHD compte N °181 810 21211 9001238 000 1 31
                <br>
                Tél: 05 37 29 50 31 Fax: 05 37 69 20 86 Email:<EMAIL> -ICE :001560529000092
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
    .logo {
        color: #4CAF50;
        font-size: 32px;
        font-weight: bold;
        text-transform: uppercase;
        display: flex;
        align-items: center;
    }
    .logo-icon {
        width: 50px;
        height: 50px;
        background-color: #4CAF50;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
    }
    .logo-icon span {
        color: white;
        font-size: 24px;
    }
</style>
{% endblock %}
