<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بون تسليم رقم {{ delivery_note.delivery_note_number }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/print_professional.css') }}">
    <style>
        /* تخصيصات إضافية لبونات التسليم */
        .delivery-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .delivery-title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .delivery-subtitle {
            text-align: center;
            font-size: 12pt;
            opacity: 0.9;
        }
        
        .delivery-status {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 10pt;
            text-align: center;
            margin: 10px 0;
        }
        
        .status-delivered {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .delivery-instructions {
            background: #e7f3ff;
            padding: 10px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
            font-size: 10pt;
        }
        
        .quality-check {
            background: #f8f9fa;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .quality-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #007bff;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 10pt;
        }
        
        .check-box {
            width: 15px;
            height: 15px;
            border: 2px solid #007bff;
            margin-left: 10px;
            display: inline-block;
        }
        
        .signature-section-enhanced {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin: 30px 0;
        }
        
        .signature-box-enhanced {
            border: 1px solid #333;
            padding: 15px;
            text-align: center;
            min-height: 100px;
        }
        
        .signature-title-enhanced {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 10pt;
            color: #007bff;
        }
        
        .signature-line-enhanced {
            border-bottom: 1px solid #333;
            margin: 25px 5px 8px;
        }
        
        .signature-date-enhanced {
            font-size: 9pt;
            color: #666;
        }
    </style>
</head>
<body class="delivery-document">
    <!-- أزرار التحكم -->
    <div class="control-buttons no-print">
        <button class="btn btn-print" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-close" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="document-container">
        <!-- ترويسة بون التسليم -->
        <div class="delivery-header">
            <div class="delivery-title">بون تسليم</div>
            <div class="delivery-subtitle">BON DE LIVRAISON / DELIVERY NOTE</div>
        </div>

        <!-- حالة التسليم -->
        <div class="delivery-status 
            {% if delivery_note.status == 'delivered' %}status-delivered
            {% elif delivery_note.status == 'pending' %}status-pending
            {% elif delivery_note.status == 'cancelled' %}status-cancelled
            {% endif %}">
            {% if delivery_note.status == 'delivered' %}✓ تم التسليم
            {% elif delivery_note.status == 'pending' %}⏳ في انتظار التسليم
            {% elif delivery_note.status == 'cancelled' %}❌ ملغي
            {% else %}{{ delivery_note.status }}{% endif %}
        </div>

        <!-- معلومات الشركة والعميل -->
        <div class="main-info">
            <div class="info-section">
                <div class="section-title">معلومات الشركة</div>
                {% if company %}
                <div class="company-name">{{ company.name }}</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ company.address }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ company.phone }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">البريد:</span>
                    <span class="info-value">{{ company.email }}</span>
                </div>
                {% else %}
                <div class="company-name">شركة إدارة طفايات الحريق</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">الرباط، المغرب</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">+212 5 37 XX XX XX</span>
                </div>
                {% endif %}
            </div>

            <div class="info-section">
                <div class="section-title">تسليم إلى</div>
                {% if delivery_note.client %}
                <div class="client-name">{{ delivery_note.client.name }}</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ delivery_note.client.address or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المدينة:</span>
                    <span class="info-value">{{ delivery_note.client.city or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ delivery_note.client.phone or '-' }}</span>
                </div>
                {% else %}
                <div class="client-name">عميل غير محدد</div>
                {% endif %}
            </div>

            <div class="info-section">
                <div class="section-title">تفاصيل التسليم</div>
                <div class="info-item">
                    <span class="info-label">رقم البون:</span>
                    <span class="info-value">{{ delivery_note.delivery_note_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ التسليم:</span>
                    <span class="info-value">{{ delivery_note.date.strftime('%d/%m/%Y') if delivery_note.date else '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الوقت:</span>
                    <span class="info-value">{{ delivery_note.date.strftime('%H:%M') if delivery_note.date else '-' }}</span>
                </div>
                {% if delivery_note.invoice %}
                <div class="info-item">
                    <span class="info-label">الفاتورة:</span>
                    <span class="info-value">{{ delivery_note.invoice.invoice_number }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- تعليمات التسليم -->
        <div class="delivery-instructions">
            <strong>تعليمات التسليم:</strong>
            يرجى التحقق من جميع المنتجات قبل التوقيع على الاستلام. في حالة وجود أي تلف أو نقص، يرجى الإشارة إليه في قسم الملاحظات.
        </div>

        <!-- جدول المنتجات -->
        <div class="table-container">
            <table class="professional-table">
                <thead>
                    <tr>
                        <th class="col-number">#</th>
                        <th class="col-description">الوصف</th>
                        <th class="col-unit">الوحدة</th>
                        <th class="col-quantity">الكمية المطلوبة</th>
                        <th class="col-quantity">الكمية المسلمة</th>
                        <th style="width: 15%;">الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in delivery_note.items %}
                    <tr>
                        <td class="col-number">{{ loop.index }}</td>
                        <td class="col-description">
                            <strong>{{ item.product.name if item.product else 'منتج محذوف' }}</strong>
                            {% if item.product and item.product.reference %}
                            <br><small>المرجع: {{ item.product.reference }}</small>
                            {% endif %}
                            {% if item.product and item.product.description %}
                            <br><small>{{ item.product.description }}</small>
                            {% endif %}
                        </td>
                        <td class="col-unit">قطعة</td>
                        <td class="col-quantity">{{ item.quantity }}</td>
                        <td class="col-quantity">_______</td>
                        <td style="text-align: center;">
                            <div class="check-box"></div>
                        </td>
                    </tr>
                    {% endfor %}
                    
                    <!-- صفوف فارغة للمظهر -->
                    {% for i in range(5 - delivery_note.items|length) %}
                    {% if i >= 0 %}
                    <tr class="empty-row">
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- قسم فحص الجودة -->
        <div class="quality-check">
            <div class="quality-title">فحص الجودة والسلامة:</div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>تم فحص جميع المنتجات والتأكد من سلامتها</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>تم التحقق من مطابقة الكميات للطلب</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>تم التأكد من صحة المراجع والأرقام التسلسلية</span>
            </div>
            <div class="check-item">
                <span class="check-box"></span>
                <span>تم تسليم شهادات الضمان والكتيبات</span>
            </div>
        </div>

        <!-- ملاحظات -->
        {% if delivery_note.notes %}
        <div class="delivery-instructions">
            <strong>ملاحظات:</strong> {{ delivery_note.notes }}
        </div>
        {% else %}
        <div class="delivery-instructions">
            <strong>ملاحظات إضافية:</strong>
            <div style="border-bottom: 1px solid #ccc; margin: 10px 0; height: 20px;"></div>
            <div style="border-bottom: 1px solid #ccc; margin: 10px 0; height: 20px;"></div>
        </div>
        {% endif %}

        <!-- قسم التوقيعات المحسن -->
        <div class="signature-section-enhanced">
            <div class="signature-box-enhanced">
                <div class="signature-title-enhanced">المرسل</div>
                <div class="signature-line-enhanced"></div>
                <div class="signature-date-enhanced">الاسم: ___________</div>
                <div class="signature-date-enhanced">التوقيع: ___________</div>
                <div class="signature-date-enhanced">التاريخ: ___________</div>
            </div>
            <div class="signature-box-enhanced">
                <div class="signature-title-enhanced">المستلم</div>
                <div class="signature-line-enhanced"></div>
                <div class="signature-date-enhanced">الاسم: ___________</div>
                <div class="signature-date-enhanced">التوقيع: ___________</div>
                <div class="signature-date-enhanced">التاريخ: ___________</div>
            </div>
            <div class="signature-box-enhanced">
                <div class="signature-title-enhanced">الشاهد</div>
                <div class="signature-line-enhanced"></div>
                <div class="signature-date-enhanced">الاسم: ___________</div>
                <div class="signature-date-enhanced">التوقيع: ___________</div>
                <div class="signature-date-enhanced">التاريخ: ___________</div>
            </div>
        </div>

        <!-- تذييل الوثيقة -->
        <div class="document-footer">
            <div class="footer-content">
                {% if company and company.footer_text %}
                <p>{{ company.footer_text|nl2br }}</p>
                {% else %}
                <p>شكراً لثقتكم بنا - نحن في خدمتكم دائماً</p>
                <p>للاستفسارات: <EMAIL> | الهاتف: +212 5 37 XX XX XX</p>
                <p><strong>تنبيه:</strong> يرجى الاحتفاظ بهذا البون كإثبات للتسليم</p>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
