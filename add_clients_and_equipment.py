#!/usr/bin/env python3
"""
Scrip<PERSON> to add sample clients and equipment maintenance data
"""

from datetime import datetime, timedelta
from app import create_app, db
from app.models import Client, EquipmentMaintenance

def add_sample_data():
    """Add sample clients and equipment maintenance records"""
    app = create_app()
    
    with app.app_context():
        try:
            # Clear existing data
            EquipmentMaintenance.query.delete()
            Client.query.delete()
            
            # Add sample clients
            clients_data = [
                {
                    'name': 'Société ABC SARL',
                    'address': '123 Rue Mohammed V',
                    'city': 'Casablanca',
                    'postal_code': '20000',
                    'email': '<EMAIL>',
                    'phone': '+212 522 123 456'
                },
                {
                    'name': 'Entreprise XYZ',
                    'address': '456 Avenue Hassan II',
                    'city': 'Rabat',
                    'postal_code': '10000',
                    'email': '<EMAIL>',
                    'phone': '+212 537 789 012'
                },
                {
                    'name': 'Hôtel Royal Palace',
                    'address': '789 Boulevard Zerktouni',
                    'city': 'Marrakech',
                    'postal_code': '40000',
                    'email': '<EMAIL>',
                    'phone': '+212 524 345 678'
                },
                {
                    'name': 'Usine Textile Moderne',
                    'address': '321 Zone Industrielle',
                    'city': 'Fès',
                    'postal_code': '30000',
                    'email': '<EMAIL>',
                    'phone': '+212 535 901 234'
                }
            ]
            
            clients = []
            for client_data in clients_data:
                client = Client(**client_data)
                db.session.add(client)
                clients.append(client)
            
            db.session.commit()
            print(f"✅ Added {len(clients)} clients")
            
            # Add sample equipment data
            equipment_data = [
                # Client 1: Société ABC SARL
                {
                    'client_id': clients[0].id,
                    'designation': 'Extincteur CO2 5kg - Bureau Direction',
                    'quantity': 2,
                    'supply_date': datetime.now().date() - timedelta(days=300),
                    'verification_date': datetime.now().date() - timedelta(days=200),
                    'current_situation': 'verification',
                    'warranty_end_date': datetime.now().date() + timedelta(days=400),
                    'recharge_end_date': datetime.now().date() + timedelta(days=100),
                    'verification_end_date': datetime.now().date() + timedelta(days=5),
                    'replacement_end_date': datetime.now().date() + timedelta(days=3000),
                    'notes': 'Vérification annuelle requise bientôt'
                },
                {
                    'client_id': clients[0].id,
                    'designation': 'Extincteur Poudre ABC 6kg - Atelier',
                    'quantity': 3,
                    'supply_date': datetime.now().date() - timedelta(days=500),
                    'verification_date': datetime.now().date() - timedelta(days=100),
                    'current_situation': 'recharge',
                    'warranty_end_date': datetime.now().date() - timedelta(days=100),
                    'recharge_end_date': datetime.now().date() - timedelta(days=2),
                    'verification_end_date': datetime.now().date() + timedelta(days=200),
                    'replacement_end_date': datetime.now().date() + timedelta(days=2500),
                    'notes': 'Recharge urgente nécessaire'
                },
                
                # Client 2: Entreprise XYZ
                {
                    'client_id': clients[1].id,
                    'designation': 'Extincteur Mousse AFFF 9L - Cuisine',
                    'quantity': 1,
                    'supply_date': datetime.now().date() - timedelta(days=100),
                    'verification_date': datetime.now().date() - timedelta(days=50),
                    'current_situation': 'garantie',
                    'warranty_end_date': datetime.now().date() + timedelta(days=600),
                    'recharge_end_date': datetime.now().date() + timedelta(days=300),
                    'verification_end_date': datetime.now().date() + timedelta(days=300),
                    'replacement_end_date': datetime.now().date() + timedelta(days=3500),
                    'notes': 'Nouvel équipement, tout est OK'
                },
                {
                    'client_id': clients[1].id,
                    'designation': 'Extincteur CO2 2kg - Salle serveur',
                    'quantity': 4,
                    'supply_date': datetime.now().date() - timedelta(days=800),
                    'verification_date': datetime.now().date() - timedelta(days=300),
                    'current_situation': 'changement',
                    'warranty_end_date': datetime.now().date() - timedelta(days=200),
                    'recharge_end_date': datetime.now().date() + timedelta(days=50),
                    'verification_end_date': datetime.now().date() + timedelta(days=50),
                    'replacement_end_date': datetime.now().date() + timedelta(days=8),
                    'notes': 'Remplacement prévu prochainement'
                },
                
                # Client 3: Hôtel Royal Palace
                {
                    'client_id': clients[2].id,
                    'designation': 'Extincteur Poudre ABC 1kg - Chambres',
                    'quantity': 10,
                    'supply_date': datetime.now().date() - timedelta(days=200),
                    'verification_date': datetime.now().date() - timedelta(days=100),
                    'current_situation': 'verification',
                    'warranty_end_date': datetime.now().date() + timedelta(days=500),
                    'recharge_end_date': datetime.now().date() + timedelta(days=200),
                    'verification_end_date': datetime.now().date(),
                    'replacement_end_date': datetime.now().date() + timedelta(days=2800),
                    'notes': 'Vérification expire aujourd\'hui!'
                },
                {
                    'client_id': clients[2].id,
                    'designation': 'Extincteur CO2 5kg - Restaurant',
                    'quantity': 3,
                    'supply_date': datetime.now().date() - timedelta(days=150),
                    'verification_date': datetime.now().date() - timedelta(days=50),
                    'current_situation': 'garantie',
                    'warranty_end_date': datetime.now().date() + timedelta(days=550),
                    'recharge_end_date': datetime.now().date() + timedelta(days=250),
                    'verification_end_date': datetime.now().date() + timedelta(days=250),
                    'replacement_end_date': datetime.now().date() + timedelta(days=3200),
                    'notes': 'Équipement en bon état'
                },
                
                # Client 4: Usine Textile Moderne
                {
                    'client_id': clients[3].id,
                    'designation': 'Extincteur Poudre ABC 9kg - Atelier Production',
                    'quantity': 8,
                    'supply_date': datetime.now().date() - timedelta(days=400),
                    'verification_date': datetime.now().date() - timedelta(days=200),
                    'current_situation': 'recharge',
                    'warranty_end_date': datetime.now().date() + timedelta(days=300),
                    'recharge_end_date': datetime.now().date() + timedelta(days=7),
                    'verification_end_date': datetime.now().date() + timedelta(days=100),
                    'replacement_end_date': datetime.now().date() + timedelta(days=2600),
                    'notes': 'Recharge prévue dans une semaine'
                }
            ]
            
            # Add equipment records
            for data in equipment_data:
                equipment = EquipmentMaintenance(**data)
                db.session.add(equipment)
            
            db.session.commit()
            
            print(f"✅ Added {len(equipment_data)} equipment records")
            
            # Show summary
            total = EquipmentMaintenance.query.count()
            expired = sum(1 for eq in EquipmentMaintenance.query.all() if eq.is_expired())
            alerts = sum(1 for eq in EquipmentMaintenance.query.all() if eq.needs_alert())
            
            print(f"\n📋 Summary:")
            print(f"  - Total clients: {len(clients)}")
            print(f"  - Total equipment: {total}")
            print(f"  - Expired: {expired}")
            print(f"  - Alerts (≤10 days): {alerts}")
            print(f"  - OK: {total - expired - alerts}")
            
            print(f"\n🔗 Visit: http://127.0.0.1:5000/equipment/maintenance")
            
        except Exception as e:
            print(f"❌ Error adding sample data: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_sample_data()
