/* ملف CSS محسن للطباعة - تصميم احترافي ونظيف */

/* التنسيق العام */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    color: #333;
    font-size: 12pt;
    line-height: 1.4;
    background: white;
}

/* الترويسة */
.header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 2px solid #dc3545;
    padding-bottom: 15px;
}

.header h2 {
    color: #dc3545;
    margin: 10px 0;
    font-size: 24pt;
    font-weight: bold;
}

.logo {
    margin-bottom: 15px;
}

.logo img {
    max-height: 80px;
    max-width: 200px;
    object-fit: contain;
}

.logo-text {
    font-size: 20pt;
    font-weight: bold;
    color: #dc3545;
    text-transform: uppercase;
}

/* معلومات الوثيقة والعميل */
.invoice-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    gap: 20px;
}

.left-section {
    flex: 1;
}

.client-info {
    flex: 1;
    border: 2px solid #dc3545;
    border-radius: 10px;
    padding: 15px;
    background-color: #f8f9fa;
}

.client-info h3 {
    color: #dc3545;
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
    font-size: 16pt;
    font-weight: bold;
}

.info-row {
    margin-bottom: 8px;
}

.info-label {
    font-weight: bold;
    display: inline-block;
    min-width: 120px;
}

/* الجداول */
.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 11pt;
}

.invoice-table th, .invoice-table td {
    border: 1px solid #333;
    padding: 8px 6px;
    text-align: center;
    vertical-align: middle;
}

.invoice-table th {
    background-color: #f2f2f2;
    font-weight: bold;
    color: #333;
}

.invoice-table td.designation {
    text-align: left;
    padding-left: 10px;
}

.invoice-table td.price {
    text-align: right;
    padding-right: 10px;
}

.empty-row td {
    height: 25px;
    border-color: #ddd;
}

/* المجاميع */
.totals {
    margin-left: auto;
    width: 350px;
    border: 1px solid #333;
    margin-bottom: 20px;
}

.totals table {
    width: 100%;
    border-collapse: collapse;
}

.totals td {
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
}

.totals td:first-child {
    font-weight: bold;
    background-color: #f8f9fa;
}

.totals td:last-child {
    text-align: right;
    font-weight: bold;
}

.totals .total-final {
    background-color: #dc3545;
    color: white;
    font-size: 14pt;
}

/* التوقيعات */
.signature-section {
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
    gap: 20px;
}

.signature-box {
    flex: 1;
    border: 2px solid #dc3545;
    border-radius: 10px;
    height: 120px;
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    font-weight: bold;
    color: #dc3545;
}

/* تفاصيل الشركة */
.company-details {
    font-size: 9pt;
    text-align: center;
    margin-top: 30px;
    border-top: 1px solid #ddd;
    padding-top: 15px;
    line-height: 1.3;
    color: #666;
}

.company-details p {
    margin: 3px 0;
}

/* أنماط خاصة للوثائق المختلفة */
.invoice-document .header {
    border-bottom-color: #28a745;
}

.invoice-document .header h2 {
    color: #28a745;
}

.invoice-document .client-info {
    border-color: #28a745;
}

.invoice-document .client-info h3 {
    color: #28a745;
}

.invoice-document .totals .total-final {
    background-color: #28a745;
}

.invoice-document .signature-box {
    border-color: #28a745;
    color: #28a745;
}

.delivery-document .header {
    border-bottom-color: #17a2b8;
}

.delivery-document .header h2 {
    color: #17a2b8;
}

.delivery-document .client-info {
    border-color: #17a2b8;
}

.delivery-document .client-info h3 {
    color: #17a2b8;
}

.delivery-document .signature-box {
    border-color: #17a2b8;
    color: #17a2b8;
}

/* تنسيقات الطباعة */
@media print {
    body {
        padding: 15px;
        font-size: 11pt;
    }
    
    .no-print {
        display: none !important;
    }
    
    .invoice-table {
        page-break-inside: avoid;
    }
    
    .header, .totals, .signature-section, .company-details {
        page-break-inside: avoid;
    }
    
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* تحسين الألوان للطباعة */
    .header h2,
    .client-info h3,
    .signature-box {
        color: #333 !important;
    }
    
    .totals .total-final {
        background-color: #333 !important;
        color: white !important;
    }
}

/* أنماط إضافية للتحسين */
.status-paid { color: #28a745; }
.status-overdue { color: #dc3545; }
.status-pending { color: #ffc107; }

.highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

.mb-10 { margin-bottom: 10px; }
.mb-15 { margin-bottom: 15px; }
.mb-20 { margin-bottom: 20px; }

.mt-10 { margin-top: 10px; }
.mt-15 { margin-top: 15px; }
.mt-20 { margin-top: 20px; }
