from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from werkzeug.security import check_password_hash
from app.forms.user_forms import LoginForm
from app.models import User
from datetime import datetime

bp = Blueprint('auth', __name__, url_prefix='/auth')

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    form = LoginForm()

    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user and check_password_hash(user.password_hash, form.password.data):
            if not user.is_active:
                flash('Votre compte est désactivé. Contactez l\'administrateur.', 'error')
                return render_template('auth/login.html', form=form)

            # Store user info in session
            session['user_id'] = user.id
            session['username'] = user.username
            session['is_admin'] = user.is_admin

            # Update last login
            user.last_login = datetime.now()
            from app import db
            db.session.commit()

            # Log login activity
            from app.models.activity_log import ActivityLog
            ActivityLog.log_login(
                user_id=user.id,
                username=user.username,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )

            flash(f'Bienvenue {user.full_name or user.username}!', 'success')

            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('main.index'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect.', 'error')

    return render_template('auth/login.html', form=form)

@bp.route('/logout')
def logout():
    """User logout"""
    user_id = session.get('user_id')
    username = session.get('username', 'Utilisateur')

    # Log logout activity
    if user_id:
        from app.models.activity_log import ActivityLog
        ActivityLog.log_logout(
            user_id=user_id,
            username=username,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )

    session.clear()
    flash(f'Au revoir {username}!', 'info')
    return redirect(url_for('auth.login'))

def login_required(f):
    """Decorator to require login"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Vous devez vous connecter pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """Decorator to require admin privileges"""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Vous devez vous connecter pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))

        if not session.get('is_admin', False):
            flash('Accès refusé. Privilèges administrateur requis.', 'error')
            return redirect(url_for('main.index'))

        return f(*args, **kwargs)
    return decorated_function
