<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/print_professional.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="quote-document">
    <!-- أزرار التحكم -->
    <div class="control-buttons no-print">
        <button class="btn btn-print" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-close" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="document-container">
        <!-- الترويسة الاحترافية -->
        <div class="document-header">
            <div class="header-content">
                <div class="company-logo">
                    {% if (company and company.logo) or (global_company and global_company.logo) %}
                        <img src="{{ url_for('static', filename=(company.logo if company else global_company.logo), _external=True) }}" alt="{{ (company.name if company else global_company.name) }}">
                    {% else %}
                        <div class="company-name">MAX AFFAIRE</div>
                    {% endif %}
                </div>
                <div class="document-info">
                    <div class="document-title">DEVIS</div>
                    <div class="document-number">N° {{ quote.quote_number }}</div>
                    <div class="document-date">{{ quote.date.strftime('%d/%m/%Y') }}</div>
                </div>
            </div>
        </div>

        <!-- قسم المعلومات الرئيسي -->
        <div class="main-info">
            <div class="info-grid">
                <!-- معلومات الوثيقة -->
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-file-alt"></i> معلومات الوثيقة
                    </div>
                    <div class="info-item">
                        <span class="info-label">التاريخ</span>
                        <span class="info-value">{{ quote.date.strftime('%d/%m/%Y') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">رقم الديفيس</span>
                        <span class="info-value">{{ quote.quote_number }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">صالح حتى</span>
                        <span class="info-value">{{ quote.expiration_date.strftime('%d/%m/%Y') if quote.expiration_date else 'غير محدد' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">الحالة</span>
                        <span class="info-value">
                            {% if quote.status == 'draft' %}مسودة
                            {% elif quote.status == 'sent' %}مرسل
                            {% elif quote.status == 'accepted' %}مقبول
                            {% elif quote.status == 'rejected' %}مرفوض
                            {% else %}{{ quote.status }}{% endif %}
                        </span>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="info-section client-section">
                    <div class="section-title">
                        <i class="fas fa-user-tie"></i> معلومات العميل
                    </div>
                    <div class="client-name">{{ quote.client.name }}</div>
                    {% if quote.client.address %}
                    <div class="info-item">
                        <span class="info-label">العنوان</span>
                        <span class="info-value">{{ quote.client.address }}</span>
                    </div>
                    {% endif %}
                    {% if quote.client.city %}
                    <div class="info-item">
                        <span class="info-label">المدينة</span>
                        <span class="info-value">{{ quote.client.city }}</span>
                    </div>
                    {% endif %}
                    {% if quote.client.phone %}
                    <div class="info-item">
                        <span class="info-label">الهاتف</span>
                        <span class="info-value">{{ quote.client.phone }}</span>
                    </div>
                    {% endif %}
                    {% if quote.client.email %}
                    <div class="info-item">
                        <span class="info-label">البريد</span>
                        <span class="info-value">{{ quote.client.email }}</span>
                    </div>
                    {% endif %}
                    {% if quote.client.ice %}
                    <div class="info-item">
                        <span class="info-label">ICE</span>
                        <span class="info-value">{{ quote.client.ice }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- الجدول الاحترافي -->
        <div class="table-container">
            <table class="professional-table">
                <thead>
                    <tr>
                        <th class="col-number">N°</th>
                        <th class="col-description">DÉSIGNATION</th>
                        <th class="col-unit">UNITÉ</th>
                        <th class="col-quantity">QTÉ</th>
                        <th class="col-price">PU HT</th>
                        <th class="col-total">TOTAL HT</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in quote.items %}
                    <tr>
                        <td class="col-number">{{ loop.index }}</td>
                        <td class="col-description">{{ item.product.name }}</td>
                        <td class="col-unit">Unité</td>
                        <td class="col-quantity">{{ item.quantity }}</td>
                        <td class="col-price">{{ "%.2f"|format(item.unit_price) }} MAD</td>
                        <td class="col-total">{{ "%.2f"|format(item.total) }} MAD</td>
                    </tr>
                    {% endfor %}
                    <!-- صفوف فارغة -->
                    {% set items_count = quote.items|list|length if quote.items else 0 %}
                    {% for i in range(10 - items_count) %}
                    <tr class="empty-row">
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- قسم المجاميع المحسن -->
        <div class="totals-section">
            <div class="totals-container">
                <table class="totals-table">
                    <tr>
                        <td class="total-label">المجموع بدون ضريبة</td>
                        <td class="total-value">{{ "%.2f"|format(quote.subtotal) }} MAD</td>
                    </tr>
                    <tr>
                        <td class="total-label">ضريبة القيمة المضافة (20%)</td>
                        <td class="total-value">{{ "%.2f"|format(quote.tax_amount) }} MAD</td>
                    </tr>
                    <tr class="final-total">
                        <td class="total-label">المجموع الإجمالي</td>
                        <td class="total-value">{{ "%.2f"|format(quote.total) }} MAD</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- قسم التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-title">
                    <i class="fas fa-user-check"></i> موافقة العميل
                </div>
                <div class="signature-line"></div>
                <div class="signature-date">التاريخ: ___/___/______</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">
                    <i class="fas fa-building"></i> ختم وتوقيع الشركة
                </div>
                <div class="signature-line"></div>
                <div class="signature-date">{{ quote.date.strftime('%d/%m/%Y') }}</div>
            </div>
        </div>

        <!-- التذييل الاحترافي -->
        <div class="document-footer">
            <div class="footer-content">
                {% set comp = company if company else global_company %}
                {% if comp and comp.footer_text %}
                    {{ comp.footer_text|nl2br }}
                {% else %}
                    <p>
                        <strong>{{ comp.legal_form if comp else "SARL" }}</strong> au capital de {{ comp.capital if comp else "2 110 000,00" }} DH
                        • RC N° {{ comp.rc if comp else "64133" }} • Patente N° {{ comp.patente if comp else "27395222" }}
                        • IF {{ comp.tax_id if comp else "3346362" }} • CNSS N° {{ comp.cnss if comp else "7304405" }}
                    </p>
                    <p>
                        <i class="fas fa-map-marker-alt"></i> {{ comp.address if comp else "N° 19, Amal 14 CYM Hay Elkhier Rabat" }}
                        • <i class="fas fa-phone"></i> {{ comp.phone if comp else "05 37 29 50 31" }}
                        • <i class="fas fa-envelope"></i> {{ comp.email if comp else "<EMAIL>" }}
                        • ICE {{ comp.ice if comp else "001560529000092" }}
                    </p>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
