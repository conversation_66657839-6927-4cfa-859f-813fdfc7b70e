#!/usr/bin/env python3
"""
Script to add sample equipment maintenance data
"""

from datetime import datetime, timedelta
from app import create_app, db
from app.models import EquipmentMaintenance

def add_sample_equipment():
    """Add sample equipment maintenance records"""
    app = create_app()
    
    with app.app_context():
        try:
            # Clear existing data
            EquipmentMaintenance.query.delete()
            
            # Sample equipment data
            equipment_data = [
                {
                    'designation': 'Extincteur CO2 5kg - Bureau Direction',
                    'quantity': 2,
                    'supply_date': datetime.now().date() - timedelta(days=300),
                    'verification_date': datetime.now().date() - timedelta(days=200),
                    'current_situation': 'verification',
                    'warranty_end_date': datetime.now().date() + timedelta(days=400),
                    'recharge_end_date': datetime.now().date() + timedelta(days=100),
                    'verification_end_date': datetime.now().date() + timedelta(days=5),  # Alert in 5 days
                    'replacement_end_date': datetime.now().date() + timedelta(days=3000),
                    'notes': 'Vérification annuelle requise bientôt'
                },
                {
                    'designation': 'Extincteur Poudre ABC 6kg - Atelier',
                    'quantity': 3,
                    'supply_date': datetime.now().date() - timedelta(days=500),
                    'verification_date': datetime.now().date() - timedelta(days=100),
                    'current_situation': 'recharge',
                    'warranty_end_date': datetime.now().date() - timedelta(days=100),  # Expired
                    'recharge_end_date': datetime.now().date() - timedelta(days=2),  # Expired 2 days ago
                    'verification_end_date': datetime.now().date() + timedelta(days=200),
                    'replacement_end_date': datetime.now().date() + timedelta(days=2500),
                    'notes': 'Recharge urgente nécessaire'
                },
                {
                    'designation': 'Extincteur Mousse AFFF 9L - Cuisine',
                    'quantity': 1,
                    'supply_date': datetime.now().date() - timedelta(days=100),
                    'verification_date': datetime.now().date() - timedelta(days=50),
                    'current_situation': 'garantie',
                    'warranty_end_date': datetime.now().date() + timedelta(days=600),
                    'recharge_end_date': datetime.now().date() + timedelta(days=300),
                    'verification_end_date': datetime.now().date() + timedelta(days=300),
                    'replacement_end_date': datetime.now().date() + timedelta(days=3500),
                    'notes': 'Nouvel équipement, tout est OK'
                },
                {
                    'designation': 'Extincteur CO2 2kg - Salle serveur',
                    'quantity': 2,
                    'supply_date': datetime.now().date() - timedelta(days=800),
                    'verification_date': datetime.now().date() - timedelta(days=300),
                    'current_situation': 'changement',
                    'warranty_end_date': datetime.now().date() - timedelta(days=200),
                    'recharge_end_date': datetime.now().date() + timedelta(days=50),
                    'verification_end_date': datetime.now().date() + timedelta(days=50),
                    'replacement_end_date': datetime.now().date() + timedelta(days=8),  # Alert in 8 days
                    'notes': 'Remplacement prévu prochainement'
                },
                {
                    'designation': 'Extincteur Poudre ABC 1kg - Véhicule',
                    'quantity': 4,
                    'supply_date': datetime.now().date() - timedelta(days=200),
                    'verification_date': datetime.now().date() - timedelta(days=100),
                    'current_situation': 'verification',
                    'warranty_end_date': datetime.now().date() + timedelta(days=500),
                    'recharge_end_date': datetime.now().date() + timedelta(days=200),
                    'verification_end_date': datetime.now().date(),  # Expires today!
                    'replacement_end_date': datetime.now().date() + timedelta(days=2800),
                    'notes': 'Vérification expire aujourd\'hui!'
                }
            ]
            
            # Add equipment records
            for data in equipment_data:
                equipment = EquipmentMaintenance(**data)
                db.session.add(equipment)
            
            db.session.commit()
            
            print("✅ Sample equipment data added successfully!")
            print(f"📊 Added {len(equipment_data)} equipment records")
            
            # Show summary
            total = EquipmentMaintenance.query.count()
            expired = sum(1 for eq in EquipmentMaintenance.query.all() if eq.is_expired())
            alerts = sum(1 for eq in EquipmentMaintenance.query.all() if eq.needs_alert())
            
            print(f"\n📋 Summary:")
            print(f"  - Total equipment: {total}")
            print(f"  - Expired: {expired}")
            print(f"  - Alerts (≤10 days): {alerts}")
            print(f"  - OK: {total - expired - alerts}")
            
            print(f"\n🔗 Visit: http://127.0.0.1:5000/equipment/maintenance")
            
        except Exception as e:
            print(f"❌ Error adding sample data: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    add_sample_equipment()
