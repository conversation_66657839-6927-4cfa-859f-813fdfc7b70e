// Custom JavaScript for the application

document.addEventListener('DOMContentLoaded', function() {
    // Enable tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Enable popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Fix dropdown z-index issues
    fixDropdownZIndex();

    // Re-fix dropdowns when they are shown
    document.addEventListener('shown.bs.dropdown', function (e) {
        fixDropdownZIndex();
    });

    // Auto-hide flash messages after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Product price calculation in forms
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit_price');
    const totalDisplay = document.getElementById('total_display');

    if (quantityInput && unitPriceInput && totalDisplay) {
        const calculateTotal = function() {
            const quantity = parseFloat(quantityInput.value) || 0;
            const unitPrice = parseFloat(unitPriceInput.value) || 0;
            const total = quantity * unitPrice;
            totalDisplay.textContent = total.toFixed(2) + ' MAD';
        };

        quantityInput.addEventListener('input', calculateTotal);
        unitPriceInput.addEventListener('input', calculateTotal);

        // Initial calculation
        calculateTotal();
    }

    // Print functionality
    const printButtons = document.querySelectorAll('.btn-print');
    if (printButtons) {
        printButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    }

    // Confirm delete
    const deleteButtons = document.querySelectorAll('.btn-delete');
    if (deleteButtons) {
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.')) {
                    e.preventDefault();
                }
            });
        });
    }

    // Quantity increment/decrement buttons
    const quantityInputs = document.querySelectorAll('input[type="number"], input[name*="quantity"]');
    quantityInputs.forEach(input => {
        const wrapper = document.createElement('div');
        wrapper.className = 'input-group';

        const decrementBtn = document.createElement('button');
        decrementBtn.className = 'btn btn-outline-secondary';
        decrementBtn.type = 'button';
        decrementBtn.innerHTML = '<i class="fas fa-minus"></i>';
        decrementBtn.addEventListener('click', function() {
            const currentValue = parseInt(input.value) || 0;
            if (currentValue > 0) {
                input.value = currentValue - 1;
                input.dispatchEvent(new Event('input'));
            }
        });

        const incrementBtn = document.createElement('button');
        incrementBtn.className = 'btn btn-outline-secondary';
        incrementBtn.type = 'button';
        incrementBtn.innerHTML = '<i class="fas fa-plus"></i>';
        incrementBtn.addEventListener('click', function() {
            const currentValue = parseInt(input.value) || 0;
            input.value = currentValue + 1;
            input.dispatchEvent(new Event('input'));
        });

        // Only add buttons if input is for quantity and not already wrapped
        if (input.name && input.name.includes('quantity') && !input.parentElement.classList.contains('input-group')) {
            input.parentNode.insertBefore(wrapper, input);
            wrapper.appendChild(decrementBtn);
            wrapper.appendChild(input);
            wrapper.appendChild(incrementBtn);
        }
    });

    // Form validation enhancement
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Check both [required] and WTForms required fields
            const requiredFields = form.querySelectorAll('[required], select[name="client_id"], select[name="status"], input[name="tax_rate"]');
            let isValid = true;
            let emptyFields = [];

            requiredFields.forEach(field => {
                let fieldValue = field.value;

                // Special handling for select fields
                if (field.tagName === 'SELECT') {
                    fieldValue = field.value && field.value !== '0' && field.value !== '' ? field.value : '';
                }

                if (!fieldValue || fieldValue.trim() === '') {
                    field.classList.add('is-invalid');
                    isValid = false;

                    // Get field label
                    const label = form.querySelector(`label[for="${field.name}"], label[for="${field.id}"]`);
                    const fieldName = label ? label.textContent.replace('*', '').trim() : field.name;
                    emptyFields.push(fieldName);
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                const message = emptyFields.length > 0
                    ? `Veuillez remplir les champs obligatoires suivants:\n• ${emptyFields.join('\n• ')}`
                    : 'Veuillez remplir tous les champs obligatoires.';
                alert(message);
            }
        });
    });

    // Auto-save functionality for forms (optional)
    const autoSaveForms = document.querySelectorAll('form[data-autosave]');
    autoSaveForms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                const formData = new FormData(form);
                localStorage.setItem('form_' + form.id, JSON.stringify(Object.fromEntries(formData)));
            });
        });

        // Restore form data on page load
        const savedData = localStorage.getItem('form_' + form.id);
        if (savedData) {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const field = form.querySelector(`[name="${key}"]`);
                if (field) {
                    field.value = data[key];
                }
            });
        }
    });
});

// Function to fix dropdown z-index issues
function fixDropdownZIndex() {
    // Fix only shown dropdown menus
    const shownDropdownMenus = document.querySelectorAll('.dropdown-menu.show');
    shownDropdownMenus.forEach(function(menu) {
        menu.style.zIndex = '9999';
        menu.style.position = 'absolute';
        menu.style.backgroundColor = '#fff';
        menu.style.border = '1px solid rgba(0,0,0,.15)';
        menu.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
    });

    // Fix select elements
    const selectElements = document.querySelectorAll('select.form-select, select.form-control');
    selectElements.forEach(function(select) {
        select.style.zIndex = '1000';
        select.style.position = 'relative';
    });

    // Ensure dropdown containers have proper positioning
    const dropdowns = document.querySelectorAll('.dropdown, .nav-item.dropdown, .btn-group');
    dropdowns.forEach(function(dropdown) {
        dropdown.style.position = 'relative';
        dropdown.style.zIndex = '1000';
    });
}

// Additional fix for Bootstrap dropdowns
document.addEventListener('click', function(e) {
    if (e.target.matches('[data-bs-toggle="dropdown"]') || e.target.closest('[data-bs-toggle="dropdown"]')) {
        setTimeout(function() {
            fixDropdownZIndex();
        }, 10);
    }
});

// Force fix dropdowns on window resize
window.addEventListener('resize', function() {
    setTimeout(fixDropdownZIndex, 100);
});

// Force fix dropdowns on scroll
window.addEventListener('scroll', function() {
    fixDropdownZIndex();
});
