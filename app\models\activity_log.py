from app import db
from datetime import datetime

class ActivityLog(db.Model):
    """Model for tracking user activities"""
    __tablename__ = 'activity_logs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('users.id'), nullable=False)
    username = db.Column(db.String(64), nullable=False)  # Store username for deleted users
    action = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))  # IPv6 support
    user_agent = db.Column(db.Text)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Additional context fields
    module = db.Column(db.String(50))  # products, clients, quotes, etc.
    record_id = db.Column(db.Integer)  # ID of the affected record
    old_values = db.Column(db.Text)  # JSON string of old values
    new_values = db.Column(db.Text)  # JSON string of new values

    # Relationship - using string reference to avoid circular imports
    # user = db.relationship('User', backref=db.backref('activity_logs', lazy='dynamic'))

    def __repr__(self):
        return f'<ActivityLog {self.username}: {self.action} at {self.timestamp}>'

    @staticmethod
    def log_activity(user_id, username, action, description=None, module=None,
                    record_id=None, old_values=None, new_values=None,
                    ip_address=None, user_agent=None):
        """Helper method to log user activity"""
        import json

        log = ActivityLog(
            user_id=user_id,
            username=username,
            action=action,
            description=description,
            module=module,
            record_id=record_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None,
            ip_address=ip_address,
            user_agent=user_agent
        )

        db.session.add(log)
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"Error logging activity: {e}")

    @staticmethod
    def log_login(user_id, username, ip_address=None, user_agent=None):
        """Log user login"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='LOGIN',
            description=f'Utilisateur {username} s\'est connecté',
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_logout(user_id, username, ip_address=None, user_agent=None):
        """Log user logout"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='LOGOUT',
            description=f'Utilisateur {username} s\'est déconnecté',
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_create(user_id, username, module, record_id, description=None,
                  new_values=None, ip_address=None, user_agent=None):
        """Log record creation"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='CREATE',
            description=description or f'Création d\'un nouvel enregistrement dans {module}',
            module=module,
            record_id=record_id,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_update(user_id, username, module, record_id, description=None,
                  old_values=None, new_values=None, ip_address=None, user_agent=None):
        """Log record update"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='UPDATE',
            description=description or f'Modification d\'un enregistrement dans {module}',
            module=module,
            record_id=record_id,
            old_values=old_values,
            new_values=new_values,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_delete(user_id, username, module, record_id, description=None,
                  old_values=None, ip_address=None, user_agent=None):
        """Log record deletion"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='DELETE',
            description=description or f'Suppression d\'un enregistrement dans {module}',
            module=module,
            record_id=record_id,
            old_values=old_values,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_print(user_id, username, module, record_id, description=None,
                 ip_address=None, user_agent=None):
        """Log print action"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='PRINT',
            description=description or f'Impression d\'un document {module}',
            module=module,
            record_id=record_id,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_export(user_id, username, module, description=None,
                  ip_address=None, user_agent=None):
        """Log export action"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='EXPORT',
            description=description or f'Exportation de données {module}',
            module=module,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @staticmethod
    def log_import(user_id, username, module, description=None,
                  ip_address=None, user_agent=None):
        """Log import action"""
        ActivityLog.log_activity(
            user_id=user_id,
            username=username,
            action='IMPORT',
            description=description or f'Importation de données {module}',
            module=module,
            ip_address=ip_address,
            user_agent=user_agent
        )

    def get_old_values_dict(self):
        """Get old values as dictionary"""
        import json
        if self.old_values:
            try:
                return json.loads(self.old_values)
            except:
                return {}
        return {}

    def get_new_values_dict(self):
        """Get new values as dictionary"""
        import json
        if self.new_values:
            try:
                return json.loads(self.new_values)
            except:
                return {}
        return {}

    @property
    def action_icon(self):
        """Get icon for action type"""
        icons = {
            'LOGIN': 'fas fa-sign-in-alt text-success',
            'LOGOUT': 'fas fa-sign-out-alt text-warning',
            'CREATE': 'fas fa-plus text-primary',
            'UPDATE': 'fas fa-edit text-info',
            'DELETE': 'fas fa-trash text-danger',
            'PRINT': 'fas fa-print text-secondary',
            'EXPORT': 'fas fa-download text-success',
            'IMPORT': 'fas fa-upload text-primary'
        }
        return icons.get(self.action, 'fas fa-info-circle text-muted')

    @property
    def action_color(self):
        """Get color class for action type"""
        colors = {
            'LOGIN': 'success',
            'LOGOUT': 'warning',
            'CREATE': 'primary',
            'UPDATE': 'info',
            'DELETE': 'danger',
            'PRINT': 'secondary',
            'EXPORT': 'success',
            'IMPORT': 'primary'
        }
        return colors.get(self.action, 'muted')
