<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number if quote else '143' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }
        
        /* Header avec logo et titre */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 0;
        }
        
        .logo-section {
            flex: 1;
        }
        
        .logo-container {
            width: 80px;
            height: 80px;
            border: 2px solid #dc3545;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            color: #dc3545;
            overflow: hidden;
        }
        
        .logo-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        
        .title-section {
            flex: 2;
            text-align: center;
        }
        
        .document-title {
            background-color: #dc3545;
            color: white;
            font-size: 32pt;
            font-weight: bold;
            padding: 20px;
            margin: 0;
            letter-spacing: 2px;
        }
        
        /* Section des informations */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            gap: 20px;
        }
        
        .company-info, .client-info, .document-info {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
        }
        
        .company-info {
            background-color: #f8f9fa;
        }
        
        .client-info {
            background-color: #fff;
        }
        
        .document-info {
            background-color: #f8f9fa;
        }
        
        .info-title {
            font-weight: bold;
            font-size: 12pt;
            margin-bottom: 10px;
            color: #dc3545;
            border-bottom: 1px solid #dc3545;
            padding-bottom: 5px;
        }
        
        .info-line {
            margin-bottom: 5px;
            font-size: 10pt;
        }
        
        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 80px;
        }
        
        /* Tableau des articles */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 10pt;
        }
        
        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
        }
        
        .items-table td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #ddd;
            vertical-align: middle;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .items-table tr:nth-child(odd) {
            background-color: white;
        }
        
        .description-col {
            text-align: left !important;
            padding-left: 10px !important;
        }
        
        /* Section des totaux */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin: 30px 0;
        }
        
        .totals-table {
            border-collapse: collapse;
            font-size: 11pt;
        }
        
        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #ddd;
        }
        
        .totals-table .label-col {
            background-color: #f8f9fa;
            font-weight: bold;
            text-align: right;
        }
        
        .totals-table .value-col {
            text-align: right;
            min-width: 100px;
        }
        
        .total-final {
            background-color: #dc3545 !important;
            color: white !important;
            font-weight: bold;
        }
        
        /* Footer */
        .footer {
            position: fixed;
            bottom: 15mm;
            left: 15mm;
            right: 15mm;
            text-align: center;
            font-size: 9pt;
            color: #dc3545;
            border-top: 1px solid #dc3545;
            padding-top: 10px;
        }
        
        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        
        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }
        
        .btn-print {
            background: #dc3545;
            color: white;
        }
        
        .btn-close {
            background: #6c757d;
            color: white;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .footer {
                position: static;
                margin-top: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="window.close()">Fermer</button>
    </div>

    <!-- Header avec logo et titre -->
    <div class="header">
        <div class="logo-section">
            <div class="logo-container">
                {% if company and company.logo %}
                    <img src="{{ url_for('static', filename='uploads/logos/' + company.logo) }}" alt="Logo">
                {% else %}
                    LOGO
                {% endif %}
            </div>
        </div>
        <div class="title-section">
            <div class="document-title">DEVIS</div>
        </div>
        <div style="flex: 1;"></div>
    </div>

    <!-- Section des informations -->
    <div class="info-section">
        <!-- Informations de l'entreprise -->
        <div class="company-info">
            <div class="info-title">Informations de l'Entreprise</div>
            {% if company %}
                <div class="info-line">
                    <span class="info-label">Nom:</span>
                    {{ company.name or 'Nom de l\'entreprise' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Adresse:</span>
                    {{ company.address or 'Adresse de l\'entreprise' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone:</span>
                    {{ company.phone or 'Téléphone' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Email:</span>
                    {{ company.email or 'Email' }}
                </div>
                <div class="info-line">
                    <span class="info-label">ICE:</span>
                    {{ company.ice or 'ICE' }}
                </div>
            {% else %}
                <div class="info-line">
                    <span class="info-label">Nom:</span>
                    Nom de l'entreprise
                </div>
                <div class="info-line">
                    <span class="info-label">Adresse:</span>
                    Adresse de l'entreprise
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone:</span>
                    Téléphone
                </div>
                <div class="info-line">
                    <span class="info-label">Email:</span>
                    Email
                </div>
            {% endif %}
        </div>

        <!-- Informations du client -->
        <div class="client-info">
            <div class="info-title">Client</div>
            <div class="info-line">
                <span class="info-label">Nom:</span>
                {{ quote.client.name if quote and quote.client else 'Nom du client' }}
            </div>
            <div class="info-line">
                <span class="info-label">Adresse:</span>
                {{ quote.client.address if quote and quote.client else 'Adresse du client' }}
            </div>
            <div class="info-line">
                <span class="info-label">Téléphone:</span>
                {{ quote.client.phone if quote and quote.client else 'Téléphone' }}
            </div>
            <div class="info-line">
                <span class="info-label">Email:</span>
                {{ quote.client.email if quote and quote.client else 'Email' }}
            </div>
            <div class="info-line">
                <span class="info-label">ICE:</span>
                {{ quote.client.ice if quote and quote.client else 'ICE' }}
            </div>
        </div>

        <!-- Informations du devis -->
        <div class="document-info">
            <div class="info-title">Devis</div>
            <div class="info-line">
                <span class="info-label">N°:</span>
                {{ quote.quote_number if quote else '143' }}
            </div>
            <div class="info-line">
                <span class="info-label">Date:</span>
                {{ quote.date.strftime('%d/%m/%Y') if quote and quote.date else '01/06/2021' }}
            </div>
            <div class="info-line">
                <span class="info-label">Validité:</span>
                {{ quote.valid_until.strftime('%d/%m/%Y') if quote and quote.valid_until else '15/06/2021' }}
            </div>
            <div class="info-line">
                <span class="info-label">Statut:</span>
                {{ quote.status if quote else 'En attente' }}
            </div>
        </div>
    </div>

    <!-- Tableau des articles -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 10%;">N°</th>
                <th style="width: 40%;">DESIGNATION</th>
                <th style="width: 10%;">UNITE</th>
                <th style="width: 10%;">QTE</th>
                <th style="width: 15%;">PU HT</th>
                <th style="width: 15%;">TOTAL HT</th>
            </tr>
        </thead>
        <tbody>
            {% if quote and quote.items %}
                {% for item in quote.items %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td class="description-col">{{ item.product.name if item.product else item.description }}</td>
                    <td>{{ item.product.unit if item.product else 'Unité' }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ "%.2f"|format(item.unit_price) }}</td>
                    <td>{{ "%.2f"|format(item.total_price) }}</td>
                </tr>
                {% endfor %}
                <!-- Lignes vides pour remplir -->
                {% set items_count = quote.items|list|length if quote and quote.items else 0 %}
                {% for i in range(10 - items_count) %}
                <tr>
                    <td>&nbsp;</td>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Lignes vides par défaut -->
                {% for i in range(10) %}
                <tr>
                    <td>&nbsp;</td>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- Section des totaux -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label-col">Total HT</td>
                <td class="value-col">{{ "%.2f"|format(quote.subtotal) if quote else '1 123,00' }} DH</td>
            </tr>
            <tr>
                <td class="label-col">TVA (20%)</td>
                <td class="value-col">{{ "%.2f"|format(quote.tax_amount) if quote and quote.tax_amount else '224,60' }} DH</td>
            </tr>
            <tr class="total-final">
                <td class="label-col total-final">Total TTC</td>
                <td class="value-col total-final">{{ "%.2f"|format(quote.total) if quote else '1 347,60' }} DH</td>
            </tr>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% else %}
            Pied de page ( Prendre des informations de Pied de page en dans Informations de l'Entreprise )
        {% endif %}
    </div>
</body>
</html>
