from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, FloatField, IntegerField, DateField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import datetime, timedelta

class QuoteForm(FlaskForm):
    client_id = SelectField('Client', coerce=int, validators=[DataRequired()])
    date = DateField('Date', validators=[Optional()], default=datetime.utcnow)
    expiration_date = DateField('Date d\'expiration', validators=[Optional()])
    status = SelectField('Statut', choices=[
        ('draft', 'Brouillon'),
        ('sent', 'Envoyé'),
        ('accepted', 'Accepté'),
        ('rejected', 'Refusé')
    ], validators=[DataRequired()])
    tax_rate = FloatField('Taux de TVA (%)', validators=[DataRequired(), NumberRange(min=0, max=100)], default=20.0)
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class QuoteItemForm(FlaskForm):
    product_id = SelectField('Produit', coerce=int, validators=[DataRequired()])
    quantity = IntegerField('Quantité', default=1, validators=[DataRequired(), NumberRange(min=1)])
    unit_price = FloatField('Prix unitaire', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('Ajouter')

class InvoiceForm(FlaskForm):
    client_id = SelectField('Client', coerce=int, validators=[DataRequired()])
    quote_id = SelectField('Devis (optionnel)', coerce=int, validators=[Optional()])
    date = DateField('Date', validators=[Optional()], default=datetime.now)
    due_date = DateField('Date d\'échéance', validators=[Optional()])
    status = SelectField('Statut', choices=[
        ('draft', 'Brouillon'),
        ('sent', 'Envoyée'),
        ('paid', 'Payée'),
        ('overdue', 'En retard'),
        ('cancelled', 'Annulée')
    ], validators=[DataRequired()])
    tax_rate = FloatField('Taux de TVA (%)', validators=[DataRequired(), NumberRange(min=0, max=100)], default=20.0)
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class InvoiceItemForm(FlaskForm):
    product_id = SelectField('Produit', coerce=int, validators=[DataRequired()])
    quantity = IntegerField('Quantité', default=1, validators=[DataRequired(), NumberRange(min=1)])
    unit_price = FloatField('Prix unitaire', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('Ajouter')

class DeliveryNoteForm(FlaskForm):
    client_id = SelectField('Client', coerce=int, validators=[DataRequired()])
    invoice_id = SelectField('Facture (optionnel)', coerce=int, validators=[Optional()])
    date = DateField('Date', validators=[Optional()], default=datetime.now)
    delivery_date = DateField('Date de livraison prévue', validators=[Optional()])
    status = SelectField('Statut', choices=[
        ('draft', 'Brouillon'),
        ('pending', 'En attente'),
        ('delivered', 'Livré'),
        ('cancelled', 'Annulé')
    ], validators=[DataRequired()])
    delivery_address = TextAreaField('Adresse de livraison', validators=[Optional(), Length(max=500)])
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class DeliveryNoteItemForm(FlaskForm):
    product_id = SelectField('Produit', coerce=int, validators=[DataRequired()])
    quantity = IntegerField('Quantité', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('Ajouter')
