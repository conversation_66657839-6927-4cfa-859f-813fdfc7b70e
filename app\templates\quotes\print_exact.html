<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number if quote else '143' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm 10mm 15mm 10mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
            width: 210mm;
            min-height: 297mm;
        }

        /* Header rouge exactement comme le modèle */
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px 25px;
            text-align: center;
            position: relative;
            margin-bottom: 0;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-container {
            position: absolute;
            left: 50px;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            border: 2px dashed white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8pt;
            overflow: hidden;
        }

        .logo-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .document-title {
            font-size: 32pt;
            font-weight: bold;
            margin: 0;
            letter-spacing: 1px;
        }

        /* Contenu principal */
        .content {
            padding: 0;
            background: white;
        }

        /* Section Client exactement comme le modèle */
        .client-section {
            background: white;
            padding: 15px 25px 10px 25px;
            margin-bottom: 0;
            border-bottom: none;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .client-info {
            flex: 1;
            padding-right: 50px;
        }

        .quote-info {
            flex: 1;
            text-align: left;
        }

        .info-line {
            margin-bottom: 4px;
            color: #333;
            font-size: 11pt;
            line-height: 1.3;
        }

        .info-label {
            color: #333;
            font-weight: normal;
            display: inline;
        }

        /* Section Objet exactement comme le modèle */
        .object-section {
            padding: 15px 30px;
            margin-bottom: 0;
            background: white;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 12pt;
        }

        .object-line {
            border-bottom: 1px solid #dc3545;
            height: 15px;
            width: 100%;
        }

        /* Tableau exactement comme le modèle */
        .table-container {
            padding: 0 30px;
            margin-bottom: 20px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            background: white;
        }

        .items-table th {
            background: #dc3545;
            color: white;
            padding: 10px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
            height: 25px;
        }

        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .items-table tr:nth-child(odd) {
            background: white;
        }

        /* Totaux exactement comme le modèle */
        .totals-container {
            padding: 0 30px;
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }

        .totals {
            width: 200px;
            margin-top: 20px;
        }

        .total-line {
            display: flex;
            justify-content: space-between;
            padding: 6px 0;
            font-size: 11pt;
            border-bottom: 1px solid #ddd;
        }

        .total-final {
            font-weight: bold;
            font-size: 12pt;
            color: #dc3545;
            border-bottom: 2px solid #dc3545;
            margin-top: 5px;
            padding-top: 8px;
        }

        /* Service info */
        .service-info {
            padding: 0 30px;
            margin-top: 50px;
            margin-bottom: 80px;
            font-size: 10pt;
            color: #666;
            font-style: italic;
        }

        /* Footer exactement comme le modèle */
        .footer {
            position: fixed;
            bottom: 15px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #dc3545;
            font-weight: bold;
            padding: 5px 30px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .footer {
                position: static;
                margin-top: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="window.close()">Fermer</button>
    </div>

    <!-- En-tête rouge -->
    <div class="header">
        <div class="logo-container">
            {% if company and company.logo %}
                <img src="{{ url_for('static', filename='uploads/logos/' + company.logo) }}" alt="Logo">
            {% else %}
                Logo
            {% endif %}
        </div>
        <div class="document-title">Devis</div>
    </div>

    <!-- Contenu principal -->
    <div class="content">
        <!-- Section Client -->
        <div class="client-section">
            <div class="client-title">Client :</div>
            <div class="client-details">
                <div class="client-info">
                    <div class="info-line">
                        <span class="info-label">Nom du client :</span>
                        {{ quote.client.name if quote and quote.client else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Ice :</span>
                        {{ quote.client.ice if quote and quote.client and quote.client.ice else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Téléphone :</span>
                        {{ quote.client.phone if quote and quote.client else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Email :</span>
                        {{ quote.client.email if quote and quote.client else '' }}
                    </div>
                </div>
                <div class="quote-info">
                    <div class="info-line">
                        <span class="info-label">Date du devis :</span>
                        {{ quote.date.strftime('%d.%m.%Y') if quote and quote.date else '1.6.2021' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Référence du devis :</span>
                        {{ quote.quote_number if quote else '143' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Date de validité :</span>
                        {{ quote.valid_until.strftime('%d.%m.%Y') if quote and quote.valid_until else '15.6.2021' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Objet -->
        <div class="object-section">
            <div class="object-title">Objet :</div>
            <div class="object-line"></div>
        </div>

        <!-- Tableau des articles -->
        <div class="table-container">
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 40%;">Description</th>
                        <th style="width: 15%;">Unité</th>
                        <th style="width: 15%;">Quantité</th>
                        <th style="width: 15%;">Prix U.HT</th>
                        <th style="width: 15%;">Prix T.HT</th>
                    </tr>
                </thead>
                <tbody>
                    {% if quote and quote.items %}
                        {% for item in quote.items %}
                        <tr>
                            <td style="text-align: left; padding-left: 10px;">{{ item.product.name if item.product else item.description }}</td>
                            <td>{{ item.product.unit if item.product else 'Unité' }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ "%.2f"|format(item.unit_price) }}</td>
                            <td>{{ "%.2f"|format(item.total_price) }}</td>
                        </tr>
                        {% endfor %}
                        <!-- Lignes vides pour remplir l'espace -->
                        {% set items_count = quote.items|list|length if quote and quote.items else 0 %}
                        {% for i in range(5 - items_count) %}
                        <tr>
                            <td style="text-align: left;">&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>0,00</td>
                            <td>0,00</td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <!-- Lignes vides par défaut -->
                        {% for i in range(5) %}
                        <tr>
                            <td style="text-align: left;">&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>0,00</td>
                            <td>0,00</td>
                        </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- Totaux -->
        <div class="totals-container">
            <div class="totals">
                <div class="total-line">
                    <span>Total HT</span>
                    <span>{{ "%.2f"|format(quote.subtotal) if quote else '1 123,00' }}</span>
                </div>
                <div class="total-line">
                    <span>Total TVA</span>
                    <span>{{ "%.2f"|format(quote.tax_amount) if quote and quote.tax_amount else '224,6' }}</span>
                </div>
                <div class="total-line total-final">
                    <span>Total TTC</span>
                    <span>{{ "%.2f"|format(quote.total) if quote else '1 347,6' }}</span>
                </div>
            </div>
        </div>

        <!-- Informations de service -->
        <div class="service-info">
            <div>Service après-vente - Garantie :</div>
            <div>Date de début de la prestation : 14/05/2022</div>
        </div>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% else %}
            Pied de page ( Prendre des informations de Pied de page en dans Informations de l'Entreprise )
        {% endif %}
    </div>
</body>
</html>
