{% extends 'base.html' %}

{% block title %}Produits & Stock - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-6">
            <i class="fas fa-boxes me-2"></i>Produits & Stock
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                <li class="breadcrumb-item active">Produits & Stock</li>
            </ol>
        </nav>
    </div>
    <div class="col-auto">
        <div class="btn-group">
            <a href="{{ url_for('main.products_create') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i>Nouveau produit
            </a>
            <a href="{{ url_for('main.stock_movements') }}" class="btn btn-outline-info">
                <i class="fas fa-exchange-alt me-1"></i>Mouvements
            </a>
            <a href="{{ url_for('main.export_data', model_type='products') }}" class="btn btn-outline-success">
                <i class="fas fa-file-excel me-1"></i>Exporter
            </a>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center bg-primary text-white">
            <div class="card-body">
                <i class="fas fa-boxes fa-2x mb-2"></i>
                <h4>{{ products|length if products else 0 }}</h4>
                <p class="mb-0">Produits Total</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-success text-white">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x mb-2"></i>
                <h4>{{ products|selectattr('current_quantity', 'gt', 5)|list|length if products else 0 }}</h4>
                <p class="mb-0">Stock Normal</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-warning text-white">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4>{{ products|selectattr('current_quantity', 'le', 5)|selectattr('current_quantity', 'gt', 0)|list|length if products else 0 }}</h4>
                <p class="mb-0">Stock Faible</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-danger text-white">
            <div class="card-body">
                <i class="fas fa-times-circle fa-2x mb-2"></i>
                <h4>{{ products|selectattr('current_quantity', 'eq', 0)|list|length if products else 0 }}</h4>
                <p class="mb-0">Rupture Stock</p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Référence</th>
                        <th>Nom</th>
                        <th>Type</th>
                        <th class="text-center">Prix unitaire</th>
                        <th class="text-center">Stock Actuel</th>
                        <th class="text-center">Valeur Stock</th>
                        <th class="text-center">Gestion Stock</th>
                        <th class="text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            <strong>{{ product.reference }}</strong>
                            {% if product.description %}
                                <br><small class="text-muted">{{ product.description }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ product.product_type }}</span>
                        </td>
                        <td class="text-center">
                            <strong>{{ "%.2f"|format(product.unit_price) }} MAD</strong>
                        </td>
                        <td class="text-center">
                            {% if product.current_quantity <= 0 %}
                                <span class="badge bg-danger fs-6">{{ product.current_quantity }}</span>
                            {% elif product.current_quantity <= 5 %}
                                <span class="badge bg-warning fs-6">{{ product.current_quantity }}</span>
                            {% else %}
                                <span class="badge bg-success fs-6">{{ product.current_quantity }}</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong>{{ "%.2f"|format(product.current_quantity * product.unit_price) }} MAD</strong>
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-success"
                                        onclick="showStockModal({{ product.id }}, '{{ product.name }}', 'add')"
                                        title="Ajouter Stock">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button type="button" class="btn btn-danger"
                                        onclick="showStockModal({{ product.id }}, '{{ product.name }}', 'remove')"
                                        title="Retirer Stock"
                                        {% if product.current_quantity <= 0 %}disabled{% endif %}>
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('main.products_edit', id=product.id) }}"
                                   class="btn btn-outline-primary" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger btn-delete"
                                        data-bs-toggle="modal" data-bs-target="#deleteModal{{ product.id }}"
                                        title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>

                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ product.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer le produit <strong>{{ product.name }}</strong> ?</p>
                                            <p class="text-danger">Cette action est irréversible.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <form action="{{ url_for('main.products_delete', id=product.id) }}" method="post" class="d-inline">
                                                <button type="submit" class="btn btn-danger">Supprimer</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Résumé des valeurs -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Valeur Totale du Stock</h5>
                        <h3 class="text-primary">
                            {% set total_value = 0 %}
                            {% for product in products %}
                                {% set total_value = total_value + (product.current_quantity * product.unit_price) %}
                            {% endfor %}
                            {{ "%.2f"|format(total_value) }} MAD
                        </h3>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <h5 class="card-title">Quantité Totale</h5>
                        <h3 class="text-success">
                            {{ products|sum(attribute='current_quantity') }} unités
                        </h3>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun produit</h5>
            <p class="text-muted">Commencez par ajouter votre premier produit.</p>
            <a href="{{ url_for('main.products_create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Ajouter le Premier Produit
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal de gestion du stock -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockModalTitle">Gestion du Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="stockForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Produit</label>
                        <input type="text" class="form-control" id="stockProductName" readonly>
                        <input type="hidden" id="stockProductId" name="product_id">
                        <input type="hidden" id="stockMovementType" name="movement_type">
                    </div>
                    <div class="mb-3">
                        <label for="stockQuantity" class="form-label">Quantité</label>
                        <input type="number" class="form-control" id="stockQuantity" name="quantity" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label for="stockReference" class="form-label">Référence Document (optionnel)</label>
                        <input type="text" class="form-control" id="stockReference" name="reference_document">
                    </div>
                    <div class="mb-3">
                        <label for="stockNotes" class="form-label">Notes (optionnel)</label>
                        <textarea class="form-control" id="stockNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary" id="stockSubmitBtn">Confirmer</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Modal d'importation -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Importer des produits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ url_for('main.products') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">Fichier Excel</label>
                        <input type="file" class="form-control" id="importFile" name="importFile" accept=".xlsx, .xls, .csv">
                        <div class="form-text">Formats acceptés: .xlsx, .xls, .csv</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Importer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function showStockModal(productId, productName, movementType) {
    document.getElementById('stockProductId').value = productId;
    document.getElementById('stockProductName').value = productName;
    document.getElementById('stockMovementType').value = movementType;

    const modal = document.getElementById('stockModal');
    const title = document.getElementById('stockModalTitle');
    const submitBtn = document.getElementById('stockSubmitBtn');
    const form = document.getElementById('stockForm');

    if (movementType === 'add') {
        title.textContent = 'Ajouter du Stock';
        submitBtn.textContent = 'Ajouter';
        submitBtn.className = 'btn btn-success';
        form.action = '{{ url_for("main.stock_add") }}';
    } else {
        title.textContent = 'Retirer du Stock';
        submitBtn.textContent = 'Retirer';
        submitBtn.className = 'btn btn-danger';
        form.action = '{{ url_for("main.stock_remove") }}';
    }

    // Reset form
    document.getElementById('stockQuantity').value = '';
    document.getElementById('stockReference').value = '';
    document.getElementById('stockNotes').value = '';

    const stockModal = new bootstrap.Modal(modal);
    stockModal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle stock form submission
    document.getElementById('stockForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const action = this.action;

        fetch(action, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la mise à jour du stock');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Erreur lors de la mise à jour du stock');
        });
    });
});
</script>
{% endblock %}