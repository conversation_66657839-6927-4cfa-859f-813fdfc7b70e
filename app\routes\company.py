from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from app import db
from app.models.company import Company
from app.forms.company_forms import CompanyForm
import os
from werkzeug.utils import secure_filename
from datetime import datetime

bp = Blueprint('company', __name__)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif'}

@bp.route('/')
def index():
    company = Company.query.first()
    if not company:
        company = Company(name="MAX AFFAIRE")
        db.session.add(company)
        db.session.commit()
    return render_template('company/index.html', company=company)

@bp.route('/edit', methods=['GET', 'POST'])
def edit():
    company = Company.query.first()
    if not company:
        company = Company(name="MAX AFFAIRE")
        db.session.add(company)
        db.session.commit()
    
    form = CompanyForm(obj=company)
    
    if form.validate_on_submit():
        # Mise à jour des informations de base
        company.name = form.name.data
        company.phone = form.phone.data
        company.email = form.email.data
        company.address = form.address.data
        company.manager = form.manager.data
        
        # Mise à jour des informations fiscales
        company.tax_id = form.tax_id.data
        company.rc = form.rc.data
        company.patente = form.patente.data
        company.ice = form.ice.data
        company.cnss = form.cnss.data
        
        # Mise à jour du pied de page
        company.footer_text = form.footer_text.data
        
        # Gestion du logo
        logo_file = request.files.get('logo')
        if logo_file and logo_file.filename and allowed_file(logo_file.filename):
            filename = secure_filename(logo_file.filename)
            # Ajouter un timestamp pour éviter les problèmes de cache
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = f"{timestamp}_{filename}"
            
            # Créer le dossier s'il n'existe pas
            uploads_dir = os.path.join(current_app.static_folder, 'uploads')
            if not os.path.exists(uploads_dir):
                os.makedirs(uploads_dir)
            
            # Sauvegarder le fichier
            logo_path = os.path.join(uploads_dir, filename)
            logo_file.save(logo_path)
            
            # Mettre à jour le chemin du logo dans la base de données
            company.logo = f"uploads/{filename}"
        
        company.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash('Informations de l\'entreprise mises à jour avec succès!', 'success')
        return redirect(url_for('company.index'))
    
    return render_template('company/edit.html', form=form, company=company)
