{% extends "base.html" %}

{% block title %}Importation de Données{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-upload text-warning me-2"></i>
                    Importation de Données
                </h1>
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour au Tableau de Bord
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Upload Form -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-file-upload me-2"></i>Télécharger un Fichier
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-4">
                            <label for="file" class="form-label">
                                <strong>Sélectionner un fichier à importer</strong>
                            </label>
                            <input type="file" class="form-control" id="file" name="file" 
                                   accept=".csv,.xlsx,.xls,.json,.sql" required>
                            <div class="form-text">
                                Formats supportés: CSV, Excel (.xlsx, .xls), JSON, SQL
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="module" class="form-label">
                                <strong>Type de données</strong>
                            </label>
                            <select class="form-select" id="module" name="module" required>
                                <option value="">Sélectionner le type de données</option>
                                <option value="products">Produits</option>
                                <option value="clients">Clients</option>
                                <option value="quotes">Devis</option>
                                <option value="invoices">Factures</option>
                                <option value="delivery_notes">Bons de Livraison</option>
                                <option value="users">Utilisateurs</option>
                                <option value="all">Toutes les données</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="backup_before" name="backup_before" checked>
                                <label class="form-check-label" for="backup_before">
                                    Créer une sauvegarde avant l'importation
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="validate_data" name="validate_data" checked>
                                <label class="form-check-label" for="validate_data">
                                    Valider les données avant l'importation
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing">
                                <label class="form-check-label" for="update_existing">
                                    Mettre à jour les enregistrements existants
                                </label>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Important:</strong> L'importation peut prendre du temps selon la taille du fichier. 
                            Une sauvegarde sera automatiquement créée avant l'importation.
                        </div>

                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-upload me-2"></i>Commencer l'Importation
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-question-circle me-2"></i>Instructions
                    </h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">Formats de Fichiers Supportés:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-file-csv text-success me-2"></i>CSV (Comma Separated Values)</li>
                        <li><i class="fas fa-file-excel text-success me-2"></i>Excel (.xlsx, .xls)</li>
                        <li><i class="fas fa-file-code text-info me-2"></i>JSON (JavaScript Object Notation)</li>
                        <li><i class="fas fa-database text-warning me-2"></i>SQL (Structured Query Language)</li>
                    </ul>

                    <hr>

                    <h6 class="text-primary">Structure des Données:</h6>
                    <div class="accordion" id="structureAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#products">
                                    Produits
                                </button>
                            </h2>
                            <div id="products" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <small>
                                        <strong>Colonnes requises:</strong><br>
                                        - name (nom)<br>
                                        - reference (référence)<br>
                                        - unit_price (prix unitaire)<br>
                                        - product_type (type)<br>
                                        <strong>Optionnelles:</strong><br>
                                        - description, current_quantity
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" 
                                        data-bs-toggle="collapse" data-bs-target="#clients">
                                    Clients
                                </button>
                            </h2>
                            <div id="clients" class="accordion-collapse collapse">
                                <div class="accordion-body">
                                    <small>
                                        <strong>Colonnes requises:</strong><br>
                                        - name (nom)<br>
                                        <strong>Optionnelles:</strong><br>
                                        - address, city, postal_code, email, phone, ice
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="alert alert-warning alert-sm">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <small>
                            <strong>Attention:</strong> Assurez-vous que vos données respectent 
                            le format requis pour éviter les erreurs d'importation.
                        </small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-tools me-2"></i>Actions Rapides
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('admin.export_data') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-download me-2"></i>Exporter Données
                        </a>
                        <a href="{{ url_for('admin.backups') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-database me-2"></i>Voir Sauvegardes
                        </a>
                        <a href="{{ url_for('admin.activity_logs') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-list me-2"></i>Journaux d'Activité
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Imports -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-secondary">
                        <i class="fas fa-history me-2"></i>Importations Récentes
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Aucune importation récente</h6>
                        <p class="text-muted">Les importations apparaîtront ici une fois effectuées</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.accordion-button {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
}

.accordion-body {
    padding: 0.75rem;
}
</style>
{% endblock %}
