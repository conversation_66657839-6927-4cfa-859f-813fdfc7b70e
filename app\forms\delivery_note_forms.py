from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, IntegerField, DateField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import datetime

class DeliveryNoteForm(FlaskForm):
    client_id = SelectField('Client', coerce=int, validators=[DataRequired()])
    invoice_id = SelectField('Basé sur une facture', coerce=int, validators=[Optional()])
    date = DateField('Date', validators=[Optional()], default=datetime.utcnow)
    status = SelectField('Statut', choices=[
        ('draft', 'Brouillon'),
        ('delivered', 'Livré'),
        ('returned', 'Retourné')
    ], validators=[DataRequired()])
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class DeliveryNoteItemForm(FlaskForm):
    product_id = SelectField('Produit', coerce=int, validators=[DataRequired()])
    quantity = IntegerField('Quantité', validators=[DataRequired(), NumberRange(min=1)])
    submit = SubmitField('Ajouter')
