from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, Password<PERSON>ield, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional, ValidationError
from app.models.user import User

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Nom d\'utilisateur', validators=[DataRequired()])
    password = PasswordField('Mot de passe', validators=[DataRequired()])
    remember_me = BooleanField('Se souvenir de moi')
    submit = SubmitField('Connexion')

class RegistrationForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    full_name = StringField('Nom complet', validators=[Optional(), Length(max=100)])
    password = PasswordField('Mot de passe', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField('Confirmer le mot de passe', validators=[DataRequired(), EqualTo('password')])
    is_admin = BooleanField('Administrateur')
    
    # Permissions
    can_view_products = BooleanField('Voir les produits', default=True)
    can_edit_products = BooleanField('Modifier les produits')
    can_view_stock = BooleanField('Voir le stock', default=True)
    can_edit_stock = BooleanField('Modifier le stock')
    can_view_clients = BooleanField('Voir les clients', default=True)
    can_edit_clients = BooleanField('Modifier les clients')
    can_view_quotes = BooleanField('Voir les devis', default=True)
    can_edit_quotes = BooleanField('Modifier les devis')
    can_view_invoices = BooleanField('Voir les factures', default=True)
    can_edit_invoices = BooleanField('Modifier les factures')
    can_view_delivery_notes = BooleanField('Voir les bons de livraison', default=True)
    can_edit_delivery_notes = BooleanField('Modifier les bons de livraison')
    can_print_reports = BooleanField('Imprimer les rapports', default=True)
    
    submit = SubmitField('Enregistrer')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Ce nom d\'utilisateur est déjà utilisé. Veuillez en choisir un autre.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Cette adresse email est déjà utilisée. Veuillez en choisir une autre.')

class EditUserForm(FlaskForm):
    username = StringField('Nom d\'utilisateur', validators=[DataRequired(), Length(min=3, max=64)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    full_name = StringField('Nom complet', validators=[Optional(), Length(max=100)])
    password = PasswordField('Nouveau mot de passe', validators=[Optional(), Length(min=6)])
    password2 = PasswordField('Confirmer le mot de passe', validators=[EqualTo('password')])
    is_admin = BooleanField('Administrateur')
    
    # Permissions
    can_view_products = BooleanField('Voir les produits')
    can_edit_products = BooleanField('Modifier les produits')
    can_view_stock = BooleanField('Voir le stock')
    can_edit_stock = BooleanField('Modifier le stock')
    can_view_clients = BooleanField('Voir les clients')
    can_edit_clients = BooleanField('Modifier les clients')
    can_view_quotes = BooleanField('Voir les devis')
    can_edit_quotes = BooleanField('Modifier les devis')
    can_view_invoices = BooleanField('Voir les factures')
    can_edit_invoices = BooleanField('Modifier les factures')
    can_view_delivery_notes = BooleanField('Voir les bons de livraison')
    can_edit_delivery_notes = BooleanField('Modifier les bons de livraison')
    can_print_reports = BooleanField('Imprimer les rapports')
    
    submit = SubmitField('Mettre à jour')
    
    def __init__(self, original_username, original_email, *args, **kwargs):
        super(EditUserForm, self).__init__(*args, **kwargs)
        self.original_username = original_username
        self.original_email = original_email
    
    def validate_username(self, username):
        if username.data != self.original_username:
            user = User.query.filter_by(username=username.data).first()
            if user is not None:
                raise ValidationError('Ce nom d\'utilisateur est déjà utilisé. Veuillez en choisir un autre.')
    
    def validate_email(self, email):
        if email.data != self.original_email:
            user = User.query.filter_by(email=email.data).first()
            if user is not None:
                raise ValidationError('Cette adresse email est déjà utilisée. Veuillez en choisir une autre.')
