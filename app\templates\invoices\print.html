<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture N° {{ invoice.invoice_number if invoice else 'FACT-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 0mm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #28a745;
        }
        
        .company-name {
            font-size: 24pt;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .document-title {
            font-size: 20pt;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 10pt;
            margin: 10px 0;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-overdue {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .info-box {
            width: 48%;
        }
        
        .info-title {
            font-weight: bold;
            color: #28a745;
            border-bottom: 1px solid #28a745;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        
        .info-line {
            margin-bottom: 5px;
        }
        
        .table-container {
            margin: 30px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #28a745;
            color: white;
            font-weight: bold;
        }
        
        .text-right {
            text-align: right;
        }
        
        .totals {
            margin-top: 20px;
            float: right;
            width: 300px;
        }
        
        .total-line {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .total-final {
            font-weight: bold;
            font-size: 14pt;
            border-top: 2px solid #28a745;
            border-bottom: 2px solid #28a745;
            margin-top: 10px;
            padding: 10px 0;
        }
        
        .payment-info {
            background: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10pt;
            color: #666;
        }
        
        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
        
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-print {
            background: #28a745;
            color: white;
        }
        
        .btn-close {
            background: #6c757d;
            color: white;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="window.close()">Fermer</button>
    </div>

    <!-- En-tête -->
    <div class="header">
        <div class="company-name">
            {% if company and company.name %}
                {{ company.name }}
            {% else %}
                GESTION D'EXTINCTEURS
            {% endif %}
        </div>
        <div style="font-size: 12pt; color: #666;">
            {% if company and company.address %}
                {{ company.address }}<br>
            {% endif %}
            {% if company and company.phone %}
                Tél: {{ company.phone }}
            {% endif %}
            {% if company and company.email %}
                | Email: {{ company.email }}
            {% endif %}
        </div>
        <div class="document-title">FACTURE</div>
        
        <!-- Statut de paiement -->
        <div class="status-badge 
            {% if invoice and invoice.status == 'paid' %}status-paid
            {% elif invoice and invoice.status == 'overdue' %}status-overdue
            {% else %}status-pending{% endif %}">
            {% if invoice and invoice.status == 'paid' %}✓ PAYÉE
            {% elif invoice and invoice.status == 'overdue' %}⚠ EN RETARD
            {% else %}⏳ EN ATTENTE{% endif %}
        </div>
    </div>

    <!-- Informations -->
    <div class="info-section">
        <div class="info-box">
            <div class="info-title">FACTURÉ À:</div>
            {% if invoice and invoice.client %}
                <div class="info-line"><strong>{{ invoice.client.name }}</strong></div>
                {% if invoice.client.address %}
                    <div class="info-line">{{ invoice.client.address }}</div>
                {% endif %}
                {% if invoice.client.city %}
                    <div class="info-line">{{ invoice.client.city }}</div>
                {% endif %}
                {% if invoice.client.phone %}
                    <div class="info-line">Tél: {{ invoice.client.phone }}</div>
                {% endif %}
                {% if invoice.client.email %}
                    <div class="info-line">Email: {{ invoice.client.email }}</div>
                {% endif %}
                {% if invoice.client.ice %}
                    <div class="info-line">ICE: {{ invoice.client.ice }}</div>
                {% endif %}
            {% else %}
                <div class="info-line"><strong>Client Demo</strong></div>
                <div class="info-line">123 Rue Example</div>
                <div class="info-line">Casablanca, Maroc</div>
                <div class="info-line">Tél: +212 5 22 XX XX XX</div>
            {% endif %}
        </div>
        
        <div class="info-box">
            <div class="info-title">DÉTAILS DE LA FACTURE:</div>
            <div class="info-line">
                <strong>N° Facture:</strong> 
                {{ invoice.invoice_number if invoice else 'FACT-001' }}
            </div>
            <div class="info-line">
                <strong>Date émission:</strong> 
                {{ invoice.date.strftime('%d/%m/%Y') if invoice and invoice.date else '01/01/2024' }}
            </div>
            <div class="info-line">
                <strong>Date échéance:</strong> 
                {{ invoice.due_date.strftime('%d/%m/%Y') if invoice and invoice.due_date else '31/01/2024' }}
            </div>
            {% if invoice and invoice.quote %}
            <div class="info-line">
                <strong>Devis N°:</strong> {{ invoice.quote.quote_number }}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Tableau des produits -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 45%;">Désignation</th>
                    <th style="width: 10%;">Qté</th>
                    <th style="width: 15%;">Prix Unit. (DH)</th>
                    <th style="width: 15%;">Total (DH)</th>
                </tr>
            </thead>
            <tbody>
                {% if invoice and invoice.items %}
                    {% for item in invoice.items %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            <strong>{{ item.product.name if item.product else 'Produit supprimé' }}</strong>
                            {% if item.product and item.product.reference %}
                                <br><small>Réf: {{ item.product.reference }}</small>
                            {% endif %}
                        </td>
                        <td class="text-right">{{ item.quantity }}</td>
                        <td class="text-right">{{ "%.2f"|format(item.unit_price) }}</td>
                        <td class="text-right">{{ "%.2f"|format(item.total) }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <!-- Données de démonstration -->
                    <tr>
                        <td>1</td>
                        <td><strong>Extincteur CO2 5kg</strong><br><small>Réf: EXT-CO2-5</small></td>
                        <td class="text-right">2</td>
                        <td class="text-right">450.00</td>
                        <td class="text-right">900.00</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td><strong>Extincteur Poudre 6kg</strong><br><small>Réf: EXT-POU-6</small></td>
                        <td class="text-right">3</td>
                        <td class="text-right">320.00</td>
                        <td class="text-right">960.00</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td><strong>Vérification annuelle</strong><br><small>Service de maintenance</small></td>
                        <td class="text-right">5</td>
                        <td class="text-right">80.00</td>
                        <td class="text-right">400.00</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Totaux -->
    <div class="totals">
        <div class="total-line">
            <span>Sous-total:</span>
            <span>{{ "%.2f"|format(invoice.subtotal) if invoice else '2,260.00' }} DH</span>
        </div>
        {% if invoice and invoice.tax_rate and invoice.tax_rate > 0 %}
        <div class="total-line">
            <span>TVA ({{ invoice.tax_rate }}%):</span>
            <span>{{ "%.2f"|format(invoice.tax_amount) }} DH</span>
        </div>
        {% endif %}
        {% if invoice and invoice.discount and invoice.discount > 0 %}
        <div class="total-line">
            <span>Remise:</span>
            <span>-{{ "%.2f"|format(invoice.discount) }} DH</span>
        </div>
        {% endif %}
        <div class="total-line total-final">
            <span>MONTANT DÛ:</span>
            <span>{{ "%.2f"|format(invoice.total) if invoice else '2,260.00' }} DH</span>
        </div>
    </div>

    <div style="clear: both;"></div>

    <!-- Informations de paiement -->
    <div class="payment-info">
        <h4 style="color: #28a745; margin-top: 0;">Informations de paiement:</h4>
        <p><strong>Banque Populaire - Agence Rabat</strong></p>
        <p>RIB: 011 810 0021211900123800013 17</p>
        <p>SWIFT: BMCEMAMC</p>
        <p style="font-size: 10pt; margin-bottom: 0;">
            Paiement sous 30 jours. En cas de retard, des intérêts de 1,5% par mois seront appliqués.
        </p>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        <p>Merci de votre confiance</p>
        <p style="font-size: 9pt;">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% else %}
                Pour toute question: <EMAIL> | Tél: +212 5 37 XX XX XX
            {% endif %}
        </p>
    </div>
</body>
</html>
