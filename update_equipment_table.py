#!/usr/bin/env python3
"""
Scrip<PERSON> to update the equipment_maintenance table to add client_id column
"""

from app import create_app, db
from app.models import EquipmentMaintenance, Client

def update_equipment_table():
    """Update the equipment_maintenance table"""
    app = create_app()
    
    with app.app_context():
        try:
            # Check if client_id column exists
            inspector = db.inspect(db.engine)
            columns = inspector.get_columns('equipment_maintenance')
            column_names = [col['name'] for col in columns]
            
            print("Current columns:", column_names)
            
            if 'client_id' not in column_names:
                print("🔧 Adding client_id column...")
                
                # Add the client_id column
                db.engine.execute('ALTER TABLE equipment_maintenance ADD COLUMN client_id INTEGER')
                
                # Create a default client if none exists
                default_client = Client.query.first()
                if not default_client:
                    print("📝 Creating default client...")
                    default_client = Client(
                        name="Client par défaut",
                        address="Adresse non spécifiée",
                        city="Ville",
                        postal_code="00000",
                        email="<EMAIL>",
                        phone="0000000000"
                    )
                    db.session.add(default_client)
                    db.session.commit()
                
                # Update existing records to use the default client
                existing_equipment = EquipmentMaintenance.query.filter_by(client_id=None).all()
                for equipment in existing_equipment:
                    equipment.client_id = default_client.id
                
                db.session.commit()
                
                print("✅ client_id column added successfully!")
            else:
                print("✅ client_id column already exists!")
            
            # Verify the update
            inspector = db.inspect(db.engine)
            updated_columns = inspector.get_columns('equipment_maintenance')
            
            print("\n📋 Updated table columns:")
            for col in updated_columns:
                print(f"  - {col['name']}: {col['type']}")
                
        except Exception as e:
            print(f"❌ Error updating table: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    update_equipment_table()
