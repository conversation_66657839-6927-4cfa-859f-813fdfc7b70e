from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, IntegerField, HiddenField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional

class StockMovementForm(FlaskForm):
    product_id = SelectField('Produit', coerce=int, validators=[DataRequired()])
    movement_type = HiddenField('Type de mouvement', validators=[DataRequired()])
    quantity = IntegerField('Quantité', validators=[DataRequired(), NumberRange(min=1)])
    reference_document = StringField('Document de référence', validators=[Optional(), Length(max=50)])
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')
