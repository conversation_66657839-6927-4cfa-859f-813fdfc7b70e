from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, FloatField, IntegerField, DateField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import datetime

class InvoiceForm(FlaskForm):
    client_id = SelectField('Client', coerce=int, validators=[DataRequired()])
    quote_id = SelectField('Basé sur un devis', coerce=int, validators=[Optional()])
    date = DateField('Date', validators=[Optional()], default=datetime.utcnow)
    due_date = DateField('Date d\'échéance', validators=[Optional()])
    status = SelectField('Statut', choices=[
        ('draft', 'Brouillon'),
        ('sent', 'Envoyée'),
        ('paid', 'Payée'),
        ('overdue', 'En retard')
    ], validators=[DataRequired()])
    tax_rate = FloatField('Taux de TVA (%)', validators=[DataRequired(), NumberRange(min=0, max=100)], default=20.0)
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')

class InvoiceItemForm(FlaskForm):
    product_id = SelectField('Produit', coerce=int, validators=[DataRequired()])
    quantity = IntegerField('Quantité', validators=[DataRequired(), NumberRange(min=1)])
    unit_price = FloatField('Prix unitaire', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('Ajouter')
