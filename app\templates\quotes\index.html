{% extends 'base.html' %}

{% block title %}Devis - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice me-2"></i>Devis
    </h1>
    <a href="{{ url_for('quotes.create_interactive') }}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i>Nouveau devis
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if quotes %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Numéro</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Expiration</th>
                        <th>Montant</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for quote in quotes %}
                    <tr>
                        <td>{{ quote.quote_number }}</td>
                        <td>{{ quote.client.name }}</td>
                        <td>{{ quote.date.strftime('%d/%m/%Y') }}</td>
                        <td>{{ quote.expiration_date.strftime('%d/%m/%Y') if quote.expiration_date else '-' }}</td>
                        <td>{{ quote.total }} MAD</td>
                        <td>
                            {% if quote.status == 'draft' %}
                            <span class="badge bg-secondary">Brouillon</span>
                            {% elif quote.status == 'sent' %}
                            <span class="badge bg-info">Envoyé</span>
                            {% elif quote.status == 'accepted' %}
                            <span class="badge bg-success">Accepté</span>
                            {% elif quote.status == 'rejected' %}
                            <span class="badge bg-danger">Refusé</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('quotes.show', id=quote.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('quotes.edit_interactive', id=quote.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('quotes.generate_pdf', id=quote.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Générer PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-delete-3d btn-delete" data-bs-toggle="modal" data-bs-target="#deleteModal{{ quote.id }}" title="Supprimer le devis">
                                    <i class="fas fa-file-invoice"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun devis n'a été créé.
            <a href="{{ url_for('quotes.create') }}">Créer un devis</a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Modals -->
{% for quote in quotes %}
<div class="modal fade" id="deleteModal{{ quote.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ quote.id }}" aria-hidden="true" style="z-index: 1060;">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel{{ quote.id }}">Confirmer la suppression</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le devis <strong>{{ quote.quote_number }}</strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('quotes.delete', id=quote.id) }}" method="post" class="d-inline">
                    <button type="submit" class="btn btn-delete-neon">
                        <i class="fas fa-trash-alt me-2"></i>Supprimer Devis
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}
