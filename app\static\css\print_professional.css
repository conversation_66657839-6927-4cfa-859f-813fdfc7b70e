/* تصميم احترافي متقدم للطباعة */

/* إعدادات الصفحة */
@page {
    size: A4 portrait;
    margin: 10mm;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 11pt;
    line-height: 1.5;
    color: #2c3e50;
    background: #ffffff;
    padding: 0;
}

/* الحاوي الرئيسي */
.document-container {
    max-width: 210mm;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    position: relative;
}

/* الترويسة البسيطة */
.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 2px solid #333;
    margin-bottom: 20px;
}

.company-logo {
    flex: 1;
}

.company-logo img {
    max-height: 50px;
    max-width: 150px;
}

.company-name {
    font-size: 18pt;
    font-weight: bold;
    color: #333;
    text-transform: uppercase;
}

.document-info {
    text-align: right;
    flex: 1;
}

.document-title {
    font-size: 20pt;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.document-number {
    font-size: 12pt;
    color: #666;
    font-weight: normal;
}

.document-date {
    font-size: 11pt;
    color: #666;
    margin-top: 3px;
}

/* قسم المعلومات المبسط */
.main-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 20px;
}

.info-section {
    flex: 1;
    padding: 15px;
    border: 1px solid #ddd;
    background: #f9f9f9;
}

.section-title {
    font-size: 12pt;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.info-item {
    display: flex;
    margin-bottom: 5px;
    font-size: 10pt;
}

.info-label {
    font-weight: bold;
    min-width: 80px;
    color: #666;
}

.info-value {
    color: #333;
    flex: 1;
}

.client-name {
    font-size: 12pt;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
}

/* الجدول المبسط */
.table-container {
    margin-bottom: 20px;
}

.professional-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 10pt;
    border: 1px solid #333;
}

.professional-table th {
    padding: 8px;
    text-align: center;
    font-weight: bold;
    background-color: #f5f5f5;
    border: 1px solid #333;
    font-size: 10pt;
}

.professional-table td {
    padding: 6px 8px;
    vertical-align: middle;
    border: 1px solid #333;
}

.col-number {
    width: 8%;
    text-align: center;
    font-weight: 600;
    color: #7f8c8d;
}

.col-description {
    width: 50%;
    text-align: left;
    font-weight: 500;
    color: #2c3e50;
}

.col-unit {
    width: 12%;
    text-align: center;
    color: #7f8c8d;
}

.col-quantity {
    width: 10%;
    text-align: center;
    font-weight: 600;
    color: #2c3e50;
}

.col-price {
    width: 10%;
    text-align: right;
    font-weight: 600;
    color: #27ae60;
}

.col-total {
    width: 10%;
    text-align: right;
    font-weight: 700;
    color: #e74c3c;
}

.empty-row td {
    height: 35px;
    border-bottom: 1px dashed #ddd;
}

/* قسم المجاميع المبسط */
.totals-section {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.totals-container {
    min-width: 300px;
    border: 1px solid #333;
}

.totals-table {
    width: 100%;
    border-collapse: collapse;
}

.totals-table td {
    padding: 8px 12px;
    font-size: 10pt;
    border: 1px solid #333;
}

.totals-table .total-label {
    font-weight: bold;
    background: #f5f5f5;
}

.totals-table .total-value {
    text-align: right;
    font-weight: bold;
}

.totals-table .final-total {
    background: #e0e0e0;
    font-weight: bold;
}

/* قسم التوقيعات المبسط */
.signatures-section {
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
    gap: 20px;
}

.signature-box {
    flex: 1;
    border: 1px solid #333;
    padding: 15px;
    text-align: center;
    min-height: 80px;
}

.signature-title {
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 10pt;
}

.signature-line {
    border-bottom: 1px solid #333;
    margin: 20px 10px 5px;
}

.signature-date {
    font-size: 9pt;
    color: #666;
}

/* التذييل البسيط لمعلومات الشركة */
.document-footer {
    border-top: 1px solid #333;
    padding: 15px 0;
    font-size: 9pt;
    text-align: center;
    margin-top: 30px;
}

.footer-content p {
    margin: 3px 0;
    color: #333;
}

/* أزرار التحكم */
.control-buttons {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 11pt;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-print {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-print:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-close {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.btn-close:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

/* تنسيقات الطباعة */
@media print {
    body {
        font-size: 10pt;
    }

    .control-buttons {
        display: none !important;
    }

    .document-container {
        box-shadow: none;
        max-width: none;
    }

    .professional-table,
    .totals-container,
    .signature-box {
        page-break-inside: avoid;
    }

    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* أنماط خاصة للوثائق المختلفة */
.quote-document .document-header {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.quote-document .info-section {
    border-left-color: #e74c3c;
}

.quote-document .totals-table .final-total {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.invoice-document .document-header {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.invoice-document .info-section {
    border-left-color: #27ae60;
}

.invoice-document .totals-table .final-total {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.delivery-document .document-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.delivery-document .info-section {
    border-left-color: #3498db;
}

.delivery-document .signature-box:hover {
    border-color: #3498db;
}
