{% extends 'base.html' %}

{% block title %}Modifier les Informations de l'Entreprise - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-building me-2"></i>Modifier les Informations de l'Entreprise
    </h1>
    <a href="{{ url_for('company.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Informations de l'entreprise</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('company.edit') }}" enctype="multipart/form-data">
            {{ form.hidden_tag() }}
            
            <div class="row mb-4">
                <div class="col-md-12">
                    <h5>Informations générales</h5>
                    <hr>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label required">Nom de l'entreprise</label>
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="manager" class="form-label">Responsable</label>
                        {{ form.manager(class="form-control") }}
                        {% if form.manager.errors %}
                            <div class="text-danger">
                                {% for error in form.manager.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">Téléphone</label>
                        {{ form.phone(class="form-control") }}
                        {% if form.phone.errors %}
                            <div class="text-danger">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="address" class="form-label">Adresse</label>
                        {{ form.address(class="form-control", rows=3) }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="logo" class="form-label">Logo</label>
                        {% if company.logo %}
                        <div class="mb-2">
                            <img src="{{ url_for('static', filename=company.logo) }}" alt="{{ company.name }}" class="img-thumbnail" style="max-height: 100px;">
                        </div>
                        {% endif %}
                        {{ form.logo(class="form-control") }}
                        {% if form.logo.errors %}
                            <div class="text-danger">
                                {% for error in form.logo.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">Formats acceptés: JPG, PNG, GIF. Laissez vide pour conserver le logo actuel.</small>
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-12">
                    <h5>Informations fiscales</h5>
                    <hr>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="tax_id" class="form-label">IF (Identifiant Fiscal)</label>
                        {{ form.tax_id(class="form-control") }}
                        {% if form.tax_id.errors %}
                            <div class="text-danger">
                                {% for error in form.tax_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="rc" class="form-label">RC (Registre de Commerce)</label>
                        {{ form.rc(class="form-control") }}
                        {% if form.rc.errors %}
                            <div class="text-danger">
                                {% for error in form.rc.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="patente" class="form-label">Patente</label>
                        {{ form.patente(class="form-control") }}
                        {% if form.patente.errors %}
                            <div class="text-danger">
                                {% for error in form.patente.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="ice" class="form-label">ICE</label>
                        {{ form.ice(class="form-control") }}
                        {% if form.ice.errors %}
                            <div class="text-danger">
                                {% for error in form.ice.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="cnss" class="form-label">CNSS</label>
                        {{ form.cnss(class="form-control") }}
                        {% if form.cnss.errors %}
                            <div class="text-danger">
                                {% for error in form.cnss.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-12">
                    <h5>Pied de page personnalisé</h5>
                    <hr>
                </div>
                
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="footer_text" class="form-label">Texte de pied de page</label>
                        {{ form.footer_text(class="form-control", rows=5) }}
                        {% if form.footer_text.errors %}
                            <div class="text-danger">
                                {% for error in form.footer_text.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">Ce texte apparaîtra en pied de page de tous vos documents (devis, factures, bons de livraison).</small>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('company.index') }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
