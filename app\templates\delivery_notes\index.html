{% extends 'base.html' %}

{% block title %}Bon<PERSON> de Livraison - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-truck me-2"></i><PERSON><PERSON> de Livraison
    </h1>
    <a href="{{ url_for('delivery_notes.create_interactive') }}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i>Nouveau bon de livraison
    </a>
</div>

<div class="card-modern">
    <div class="card-header-modern">
        <h6 class="m-0"><i class="fas fa-shipping-fast me-2"></i>Liste des bons de livraison</h6>
    </div>
    <div class="card-body-modern">
        {% if delivery_notes %}
        <div class="table-responsive">
            <table class="table-modern table-striped table-hover">
                <thead>
                    <tr>
                        <th>N<PERSON><PERSON>ro</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Facture</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for note in delivery_notes %}
                    <tr>
                        <td>{{ note.delivery_note_number }}</td>
                        <td>{{ note.client.name }}</td>
                        <td>{{ note.date.strftime('%d/%m/%Y') }}</td>
                        <td>
                            {% if note.invoice %}
                            <a href="{{ url_for('invoices.show', id=note.invoice.id) }}">{{ note.invoice.invoice_number }}</a>
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            {% if note.status == 'draft' %}
                            <span class="badge bg-secondary">Brouillon</span>
                            {% elif note.status == 'delivered' %}
                            <span class="badge bg-success">Livré</span>
                            {% elif note.status == 'returned' %}
                            <span class="badge bg-warning">Retourné</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('delivery_notes.show', id=note.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('delivery_notes.edit_interactive', id=note.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('delivery_notes.generate_pdf', id=note.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Générer PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger"
                                        onclick="confirmDelete('{{ note.delivery_note_number }}', '{{ url_for('delivery_notes.delete', id=note.id) }}', 'le bon de livraison')"
                                        title="Supprimer le bon de livraison">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>


                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun bon de livraison n'a été créé.
            <a href="{{ url_for('delivery_notes.create') }}">Créer un bon de livraison</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
