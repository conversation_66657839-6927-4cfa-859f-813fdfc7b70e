{% extends 'base.html' %}

{% block title %}<PERSON><PERSON> <PERSON> Livraison - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-truck me-2"></i><PERSON><PERSON> de Livraison
    </h1>
    <a href="{{ url_for('delivery_notes.create_interactive') }}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i>Nouveau bon de livraison
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if delivery_notes %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Numéro</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Facture</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for note in delivery_notes %}
                    <tr>
                        <td>{{ note.delivery_note_number }}</td>
                        <td>{{ note.client.name }}</td>
                        <td>{{ note.date.strftime('%d/%m/%Y') }}</td>
                        <td>
                            {% if note.invoice %}
                            <a href="{{ url_for('invoices.show', id=note.invoice.id) }}">{{ note.invoice.invoice_number }}</a>
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            {% if note.status == 'draft' %}
                            <span class="badge bg-secondary">Brouillon</span>
                            {% elif note.status == 'delivered' %}
                            <span class="badge bg-success">Livré</span>
                            {% elif note.status == 'returned' %}
                            <span class="badge bg-warning">Retourné</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('delivery_notes.show', id=note.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('delivery_notes.edit_interactive', id=note.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('delivery_notes.generate_pdf', id=note.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Générer PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-delete" data-bs-toggle="modal" data-bs-target="#deleteModal{{ note.id }}" data-bs-toggle="tooltip" title="Supprimer le bon de livraison">
                                    <i class="fas fa-truck"></i>
                                </button>
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal fade delete-modal" id="deleteModal{{ note.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer le bon de livraison <strong>{{ note.delivery_note_number }}</strong> ?</p>
                                            <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Cette action est irréversible.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <form action="{{ url_for('delivery_notes.delete', id=note.id) }}" method="post">
                                                <button type="submit" class="btn btn-delete-confirm">
                                                    <i class="fas fa-shipping-fast me-2"></i>Supprimer Bon
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun bon de livraison n'a été créé.
            <a href="{{ url_for('delivery_notes.create') }}">Créer un bon de livraison</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
