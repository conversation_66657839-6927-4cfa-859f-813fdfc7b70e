{% extends "base.html" %}

{% block title %}Maintenance des Équipements{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-tools text-danger me-2"></i>Maintenance des Équipements
            </h1>
            <p class="text-muted">Suivi des équipements de sécurité incendie</p>
        </div>
        <a href="{{ url_for('main.equipment_maintenance_create') }}" class="btn btn-danger">
            <i class="fas fa-plus me-2"></i>Nouvel Équipement
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-fire-extinguisher fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Expirés</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ expired_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Alertes</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ alert_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">OK</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ ok_count }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts Section -->
    {% if alerts %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="fas fa-bell me-2"></i>Alertes et Notifications
            </h6>
        </div>
        <div class="card-body">
            {% for alert in alerts %}
            <div class="alert {% if alert.is_expired %}alert-danger{% else %}alert-warning{% endif %} alert-dismissible fade show" role="alert">
                <strong>{{ alert.message }}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Search and Filter -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recherche et Filtres</h6>
        </div>
        <div class="card-body">
            <form method="POST" class="row g-3">
                {{ form.hidden_tag() }}
                <div class="col-md-4">
                    {{ form.search.label(class="form-label") }}
                    {{ form.search(class="form-control") }}
                </div>
                <div class="col-md-3">
                    {{ form.situation_filter.label(class="form-label") }}
                    {{ form.situation_filter(class="form-select") }}
                </div>
                <div class="col-md-3">
                    {{ form.alert_filter.label(class="form-label") }}
                    {{ form.alert_filter(class="form-select") }}
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    {{ form.submit(class="btn btn-primary w-100") }}
                </div>
            </form>
        </div>
    </div>

    <!-- Equipment Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Liste des Équipements</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="equipmentTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Client</th>
                            <th>Désignation</th>
                            <th>Quantité</th>
                            <th>Date Fourniture</th>
                            <th>Date Vérification</th>
                            <th>Situation</th>
                            <th>Date de Fin</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for equipment in equipment_list %}
                        <tr>
                            <td>
                                <strong>{{ equipment.client.name if equipment.client else 'Client non spécifié' }}</strong>
                                {% if equipment.client and equipment.client.city %}
                                    <br><small class="text-muted">{{ equipment.client.city }}</small>
                                {% endif %}
                            </td>
                            <td>{{ equipment.designation }}</td>
                            <td>{{ equipment.quantity }}</td>
                            <td>{{ equipment.supply_date.strftime('%d/%m/%Y') if equipment.supply_date else '-' }}</td>
                            <td>{{ equipment.verification_date.strftime('%d/%m/%Y') if equipment.verification_date else '-' }}</td>
                            <td>
                                <span class="badge
                                    {% if equipment.current_situation == 'garantie' %}bg-primary
                                    {% elif equipment.current_situation == 'recharge' %}bg-info
                                    {% elif equipment.current_situation == 'verification' %}bg-warning
                                    {% elif equipment.current_situation == 'changement' %}bg-secondary
                                    {% endif %}">
                                    {{ equipment.current_situation.title() }}
                                </span>
                            </td>
                            <td>
                                {% set end_date = equipment.get_current_end_date() %}
                                {% if end_date %}
                                    <span class="{% if equipment.is_expired() %}text-danger fw-bold{% elif equipment.needs_alert() %}text-warning fw-bold{% endif %}">
                                        {{ end_date.strftime('%d/%m/%Y') }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if equipment.is_expired() %}
                                    <span class="badge bg-danger">Expiré</span>
                                {% elif equipment.needs_alert() %}
                                    <span class="badge bg-warning">Alerte</span>
                                {% else %}
                                    <span class="badge bg-success">OK</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('main.equipment_maintenance_edit', id=equipment.id) }}" class="btn btn-sm btn-outline-primary" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ equipment.id }}" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- Delete Modal -->
                        <div class="modal fade" id="deleteModal{{ equipment.id }}" tabindex="-1">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Confirmer la suppression</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        Êtes-vous sûr de vouloir supprimer l'équipement "{{ equipment.designation }}" ?
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                        <form method="POST" action="{{ url_for('main.equipment_maintenance_delete', id=equipment.id) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-danger">Supprimer</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#equipmentTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/French.json"
        },
        "order": [[ 5, "asc" ]], // Sort by end date
        "pageLength": 25
    });

    // Force fix dropdown z-index for this page
    setTimeout(function() {
        const style = document.createElement('style');
        style.textContent = `
            .form-select,
            select.form-select,
            select.form-control {
                z-index: 1000 !important;
                position: relative !important;
            }

            .card {
                z-index: 1 !important;
                position: relative !important;
            }

            .table-responsive {
                z-index: 1 !important;
                position: relative !important;
            }

            /* Ensure dropdowns appear above DataTables */
            .dataTables_wrapper {
                z-index: 1 !important;
            }

            .dataTables_filter,
            .dataTables_length {
                z-index: 1 !important;
            }

            /* Fix for select2 or custom dropdowns */
            .dropdown-menu.show {
                z-index: 99999 !important;
                position: absolute !important;
                display: block !important;
            }
        `;
        document.head.appendChild(style);

        // Force fix all select elements on this page
        const selects = document.querySelectorAll('select.form-select, select.form-control');
        selects.forEach(select => {
            select.style.zIndex = '1000';
            select.style.position = 'relative';
        });
    }, 500);
});
</script>
{% endblock %}
