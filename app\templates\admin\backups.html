{% extends "base.html" %}

{% block title %}Gestion des Sauvegardes{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-database text-success me-2"></i>
                    Gestion des Sauvegardes
                </h1>
                <div class="btn-group">
                    <form method="POST" action="{{ url_for('admin.create_backup') }}" class="d-inline">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>Créer Sauvegarde
                        </button>
                    </form>
                    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h5 mb-0">{{ backups|length }}</div>
                            <div class="small">Sauvegardes Totales</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h5 mb-0">
                                {% set manual_backups = backups|selectattr('backup_type', 'equalto', 'manual')|list %}
                                {{ manual_backups|length }}
                            </div>
                            <div class="small">Manuelles</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h5 mb-0">
                                {% set auto_backups = backups|selectattr('backup_type', 'equalto', 'automatic')|list %}
                                {{ auto_backups|length }}
                            </div>
                            <div class="small">Automatiques</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-robot fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="h5 mb-0">{{ schedules|length }}</div>
                            <div class="small">Planifications</div>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backups List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">
                <i class="fas fa-list me-2"></i>Liste des Sauvegardes
            </h6>
        </div>
        <div class="card-body">
            {% if backups %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Nom du Fichier</th>
                                <th>Type</th>
                                <th>Taille</th>
                                <th>Statut</th>
                                <th>Date de Création</th>
                                <th>Créé par</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backups %}
                            <tr>
                                <td>
                                    <strong>{{ backup.filename }}</strong>
                                    {% if backup.description %}
                                        <br><small class="text-muted">{{ backup.description }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if backup.backup_type == 'manual' else 'info' }}">
                                        <i class="{{ backup.type_icon }}"></i>
                                        {{ backup.backup_type|title }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ backup.file_size_human }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if backup.status == 'completed' else 'warning' }}">
                                        <i class="{{ backup.status_icon }}"></i>
                                        {{ backup.status|title }}
                                    </span>
                                </td>
                                <td>
                                    <small>
                                        {{ backup.created_at.strftime('%d/%m/%Y') }}<br>
                                        {{ backup.created_at.strftime('%H:%M:%S') }}
                                    </small>
                                </td>
                                <td>
                                    {% if backup.created_by %}
                                        <small class="text-muted">ID: {{ backup.created_by }}</small>
                                    {% else %}
                                        <small class="text-muted">Système</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if backup.file_exists() %}
                                            <a href="{{ url_for('admin.download_backup', backup_id=backup.id) }}" 
                                               class="btn btn-outline-primary" title="Télécharger">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        {% endif %}
                                        <button class="btn btn-outline-danger" 
                                                onclick="confirmDelete({{ backup.id }}, '{{ backup.filename }}')"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-database fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune sauvegarde trouvée</h5>
                    <p class="text-muted">Créez votre première sauvegarde pour commencer</p>
                    <form method="POST" action="{{ url_for('admin.create_backup') }}" class="d-inline">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>Créer Première Sauvegarde
                        </button>
                    </form>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Backup Schedules -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-clock me-2"></i>Planifications Automatiques
            </h6>
        </div>
        <div class="card-body">
            {% if schedules %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Fréquence</th>
                                <th>Prochaine Exécution</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for schedule in schedules %}
                            <tr>
                                <td><strong>{{ schedule.name }}</strong></td>
                                <td>{{ schedule.frequency_display }}</td>
                                <td>
                                    {% if schedule.next_run %}
                                        <small>{{ schedule.next_run.strftime('%d/%m/%Y %H:%M') }}</small>
                                    {% else %}
                                        <small class="text-muted">Non planifié</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if schedule.is_active else 'secondary' }}">
                                        {{ 'Actif' if schedule.is_active else 'Inactif' }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-secondary" disabled>
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                    <p class="text-muted">Aucune planification automatique configurée</p>
                    <button class="btn btn-sm btn-info" disabled>
                        <i class="fas fa-plus me-1"></i>Ajouter Planification
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirmer la Suppression
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la sauvegarde <strong id="backupName"></strong> ?</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Cette action est irréversible. Le fichier sera définitivement supprimé.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Supprimer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(backupId, filename) {
    document.getElementById('backupName').textContent = filename;
    document.getElementById('deleteForm').action = `/admin/backup/${backupId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
