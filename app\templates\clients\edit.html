{% extends 'base.html' %}

{% block title %}Modifier Client - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user-edit me-2"></i>Modifier Client
    </h1>
    <div>
        <a href="{{ url_for('main.clients') }}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i>Voir
        </a>
        <a href="{{ url_for('main.clients') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Informations du client</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('main.clients_edit', id=client.id) }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="name" class="form-label required">Nom du client</label>
                        {{ form.name(class="form-control", placeholder="Nom de l'entreprise ou du particulier") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="address" class="form-label">Adresse</label>
                        {{ form.address(class="form-control", rows=2, placeholder="Adresse complète") }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="postal_code" class="form-label">Code postal</label>
                        {{ form.postal_code(class="form-control", placeholder="Code postal") }}
                        {% if form.postal_code.errors %}
                            <div class="text-danger">
                                {% for error in form.postal_code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="city" class="form-label">Ville</label>
                        {{ form.city(class="form-control", placeholder="Ville") }}
                        {% if form.city.errors %}
                            <div class="text-danger">
                                {% for error in form.city.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        {{ form.email(class="form-control", placeholder="<EMAIL>") }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label">Téléphone</label>
                        {{ form.phone(class="form-control", placeholder="Numéro de téléphone") }}
                        {% if form.phone.errors %}
                            <div class="text-danger">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label for="ice" class="form-label">ICE</label>
                        {{ form.ice(class="form-control", placeholder="Identifiant Commun de l'Entreprise") }}
                        {% if form.ice.errors %}
                            <div class="text-danger">
                                {% for error in form.ice.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('main.clients') }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
