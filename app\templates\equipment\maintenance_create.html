{% extends "base.html" %}

{% block title %}Nouvel Équipement - Maintenance{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-plus text-danger me-2"></i>Nouvel Équipement
            </h1>
            <p class="text-muted">Ajouter un nouvel équipement de sécurité incendie</p>
        </div>
        <a href="{{ url_for('main.equipment_maintenance') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour à la liste
        </a>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-white">
                        <i class="fas fa-fire-extinguisher me-2"></i>Informations de l'Équipement
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Client Information -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.client_id.label(class="form-label required") }}
                                    {{ form.client_id(class="form-select", required=true) }}
                                    {% if form.client_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.client_id.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Equipment Items Section -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-white">
                                    <i class="fas fa-fire-extinguisher me-2"></i>Équipements de Sécurité Incendie
                                </h6>
                                <button type="button" class="btn btn-sm btn-outline-primary" id="add-equipment-btn">
                                    <i class="fas fa-plus me-2"></i>Ajouter ligne
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="equipment-items-container">
                                    {% for item_form in form.equipment_items %}
                                        <div class="equipment-item row mb-3">
                                            <div class="col-md-8">
                                                <div class="mb-2">
                                                    {{ item_form.designation.label(class="form-label required") }}
                                                    {{ item_form.designation(class="form-control", required=true) }}
                                                    {% if item_form.designation.errors %}
                                                        <div class="text-danger">
                                                            {% for error in item_form.designation.errors %}
                                                                {{ error }}
                                                            {% endfor %}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-2">
                                                    {{ item_form.quantity.label(class="form-label required") }}
                                                    {{ item_form.quantity(class="form-control", required=true) }}
                                                    {% if item_form.quantity.errors %}
                                                        <div class="text-danger">
                                                            {% for error in item_form.quantity.errors %}
                                                                {{ error }}
                                                            {% endfor %}
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-md-1">
                                                <div class="mb-2">
                                                    <label class="form-label">&nbsp;</label>
                                                    <button type="button" class="btn btn-outline-danger btn-sm remove-equipment-btn d-block" style="margin-top: 5px;">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>

                        <!-- Dates -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.supply_date.label(class="form-label required") }}
                                    {{ form.supply_date(class="form-control", required=true) }}
                                    {% if form.supply_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.supply_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.verification_date.label(class="form-label") }}
                                    {{ form.verification_date(class="form-control") }}
                                    {% if form.verification_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.verification_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Current Situation -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.current_situation.label(class="form-label required") }}
                                    {{ form.current_situation(class="form-select", required=true) }}
                                    {% if form.current_situation.errors %}
                                        <div class="text-danger">
                                            {% for error in form.current_situation.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- End Dates Section -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-white">
                                    <i class="fas fa-calendar-alt me-2"></i>Dates de Fin de Situation
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.warranty_end_date.label(class="form-label") }}
                                            {{ form.warranty_end_date(class="form-control") }}
                                            {% if form.warranty_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.warranty_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.recharge_end_date.label(class="form-label") }}
                                            {{ form.recharge_end_date(class="form-control") }}
                                            {% if form.recharge_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.recharge_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.verification_end_date.label(class="form-label") }}
                                            {{ form.verification_end_date(class="form-control") }}
                                            {% if form.verification_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.verification_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.replacement_end_date.label(class="form-label") }}
                                            {{ form.replacement_end_date(class="form-control") }}
                                            {% if form.replacement_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.replacement_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control") }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger">
                                            {% for error in form.notes.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.equipment_maintenance') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            {{ form.submit(class="btn btn-danger") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let equipmentItemIndex = {{ form.equipment_items|length }};

    // Auto-calculate end dates based on supply date
    const supplyDateInput = document.getElementById('supply_date');
    const warrantyEndInput = document.getElementById('warranty_end_date');
    const rechargeEndInput = document.getElementById('recharge_end_date');
    const verificationEndInput = document.getElementById('verification_end_date');
    const replacementEndInput = document.getElementById('replacement_end_date');

    if (supplyDateInput) {
        supplyDateInput.addEventListener('change', function() {
            const supplyDate = new Date(this.value);
            if (supplyDate) {
                // Warranty: 2 years
                if (!warrantyEndInput.value) {
                    const warrantyEnd = new Date(supplyDate);
                    warrantyEnd.setFullYear(warrantyEnd.getFullYear() + 2);
                    warrantyEndInput.value = warrantyEnd.toISOString().split('T')[0];
                }

                // Recharge: 1 year
                if (!rechargeEndInput.value) {
                    const rechargeEnd = new Date(supplyDate);
                    rechargeEnd.setFullYear(rechargeEnd.getFullYear() + 1);
                    rechargeEndInput.value = rechargeEnd.toISOString().split('T')[0];
                }

                // Verification: 1 year
                if (!verificationEndInput.value) {
                    const verificationEnd = new Date(supplyDate);
                    verificationEnd.setFullYear(verificationEnd.getFullYear() + 1);
                    verificationEndInput.value = verificationEnd.toISOString().split('T')[0];
                }

                // Replacement: 10 years
                if (!replacementEndInput.value) {
                    const replacementEnd = new Date(supplyDate);
                    replacementEnd.setFullYear(replacementEnd.getFullYear() + 10);
                    replacementEndInput.value = replacementEnd.toISOString().split('T')[0];
                }
            }
        });
    }

    // Add equipment item functionality
    document.getElementById('add-equipment-btn').addEventListener('click', function() {
        const container = document.getElementById('equipment-items-container');
        const newItem = document.createElement('div');
        newItem.className = 'equipment-item row mb-3';
        newItem.innerHTML = `
            <div class="col-md-8">
                <div class="mb-2">
                    <label class="form-label required">Désignation</label>
                    <input class="form-control" name="equipment_items-${equipmentItemIndex}-designation"
                           placeholder="Ex: Extincteur CO2 5kg" required type="text">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-2">
                    <label class="form-label required">Quantité</label>
                    <input class="form-control" min="1" name="equipment_items-${equipmentItemIndex}-quantity"
                           required type="number" value="1">
                </div>
            </div>
            <div class="col-md-1">
                <div class="mb-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-equipment-btn d-block" style="margin-top: 5px;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newItem);
        equipmentItemIndex++;
        updateRemoveButtons();
    });

    // Remove equipment item functionality
    function updateRemoveButtons() {
        const removeButtons = document.querySelectorAll('.remove-equipment-btn');
        const equipmentItems = document.querySelectorAll('.equipment-item');

        removeButtons.forEach(button => {
            button.addEventListener('click', function() {
                if (equipmentItems.length > 1) {
                    this.closest('.equipment-item').remove();
                    updateRemoveButtons();
                } else {
                    alert('Au moins un équipement est requis.');
                }
            });
        });

        // Hide remove button if only one item
        if (equipmentItems.length <= 1) {
            removeButtons.forEach(btn => btn.style.display = 'none');
        } else {
            removeButtons.forEach(btn => btn.style.display = 'block');
        }
    }

    // Initialize remove buttons
    updateRemoveButtons();
});
</script>
{% endblock %}
