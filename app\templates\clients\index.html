{% extends 'base.html' %}

{% block title %}Clients - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-users me-2"></i>Clients
    </h1>
    <div class="btn-group">
        <a href="{{ url_for('main.clients_create') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>Nouveau client
        </a>
        <a href="{{ url_for('main.export_data', model_type='clients') }}" class="btn btn-success">
            <i class="fas fa-file-excel me-1"></i>Exporter
        </a>
        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="fas fa-file-import me-1"></i>Importer
        </button>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if clients %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Ville</th>
                        <th>Email</th>
                        <th>Téléphone</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>{{ client.name }}</td>
                        <td>{{ client.city or '-' }}</td>
                        <td>{{ client.email or '-' }}</td>
                        <td>{{ client.phone or '-' }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('main.clients_edit', id=client.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('main.print_document', model_type='client', id=client.id) }}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Imprimer" target="_blank">
                                    <i class="fas fa-print"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-delete-neon btn-delete" data-bs-toggle="modal" data-bs-target="#deleteModal{{ client.id }}" title="Supprimer le client">
                                    <i class="fas fa-user-times"></i>
                                </button>
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ client.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer le client <strong>{{ client.name }}</strong> ?</p>
                                            <p class="text-danger">Cette action est irréversible et supprimera également tous les devis, factures et bons de livraison associés à ce client.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <form action="{{ url_for('main.clients_delete', id=client.id) }}" method="post" class="d-inline">
                                                <button type="submit" class="btn btn-delete-text">
                                                    <i class="fas fa-user-slash me-2"></i>Supprimer Client
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun client n'a été ajouté.
            <a href="{{ url_for('main.clients') }}">Ajouter un client</a>
        </div>
        {% endif %}
    </div>
</div>
<!-- Modal d'importation -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importModalLabel">Importer des clients</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ url_for('main.clients') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">Fichier Excel</label>
                        <input type="file" class="form-control" id="importFile" name="importFile" accept=".xlsx, .xls, .csv">
                        <div class="form-text">Formats acceptés: .xlsx, .xls, .csv</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Importer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Initialisation des tooltips -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    });
</script>
{% endblock %}