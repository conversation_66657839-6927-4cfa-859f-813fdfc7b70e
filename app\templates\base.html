<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Gestion d'Extincteurs{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-fire-extinguisher me-2"></i>Gestion d'Extincteurs
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>Accueil
                        </a>
                    </li>
                    <!-- Menus de navigation -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes me-1"></i>Produits & Stock
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.products') }}">Gestion Produits & Stock</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.products_create') }}">Nouveau produit</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('stock.return_stock') }}">Retour au Stock</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('stock.remove') }}">Sortie de Stock</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.stock_movements') }}">Mouvements de Stock</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-invoice-dollar me-1"></i>Commercial
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('main.commercial_quotes') }}">Devis</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.commercial_invoices') }}">Factures</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('main.commercial_delivery_notes') }}">Bons de livraison</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.clients') }}">
                            <i class="fas fa-users me-1"></i>Clients
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('main.equipment_maintenance') }}">
                            <i class="fas fa-tools me-1"></i>Maintenance
                        </a>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Administration
                        </a>
                        <ul class="dropdown-menu">
                            {% if session.is_admin %}
                            <li><a class="dropdown-item" href="{{ url_for('admin.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>Tableau de Bord Admin
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.activity_logs') }}">
                                <i class="fas fa-list me-2"></i>Journaux d'Activité
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.backups') }}">
                                <i class="fas fa-database me-2"></i>Sauvegardes
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{{ url_for('users.index') }}">
                                <i class="fas fa-users me-2"></i>Utilisateurs
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('company.index') }}">
                                <i class="fas fa-building me-2"></i>Informations de l'entreprise
                            </a></li>
                            {% if session.user_id %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© 2023 Gestion d'Extincteurs</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <!-- Delete Modals JS -->
    <script src="{{ url_for('static', filename='js/delete-modals.js') }}"></script>

    <!-- Additional dropdown fix -->
    <script>
    // Force fix for dropdown z-index issues
    document.addEventListener('DOMContentLoaded', function() {
        // Apply immediate fix
        setTimeout(function() {
            const style = document.createElement('style');
            style.textContent = `
                .dropdown-menu.show,
                .nav-item .dropdown-menu.show,
                .navbar .dropdown-menu.show,
                .btn-group .dropdown-menu.show {
                    z-index: 99999 !important;
                    position: absolute !important;
                    background-color: #ffffff !important;
                    border: 1px solid rgba(0,0,0,.15) !important;
                    border-radius: 0.375rem !important;
                    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
                    display: block !important;
                }
                .dropdown-item {
                    color: #212529 !important;
                    background-color: transparent !important;
                }
                .dropdown-item:hover {
                    background-color: #f8f9fa !important;
                    color: #212529 !important;
                }
            `;
            document.head.appendChild(style);
        }, 100);

        // Fix on dropdown show
        document.addEventListener('shown.bs.dropdown', function(e) {
            const menu = e.target.querySelector('.dropdown-menu');
            if (menu) {
                menu.style.zIndex = '99999';
                menu.style.position = 'absolute';
                menu.style.backgroundColor = '#ffffff';
            }
        });
    });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
