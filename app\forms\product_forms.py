from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, FloatField, IntegerField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional

class ProductForm(FlaskForm):
    name = StringField('Nom du produit', validators=[DataRequired(), Length(min=2, max=100)])
    product_type = SelectField('Type de produit', choices=[
        ('Extincteur', 'Extincteur'),
        ('RIA', 'RIA (Robinet d\'Incendie Armé)'),
        ('Détection', 'Système de détection'),
        ('Signalisation', 'Signalisation de sécurité'),
        ('Accessoire', 'Accessoire'),
        ('Autre', 'Autre')
    ], validators=[DataRequired()])
    reference = StringField('Référence', validators=[DataRequired(), Length(min=2, max=50)])
    unit_price = FloatField('Prix unitaire (MAD)', validators=[DataRequired(), NumberRange(min=0)])
    current_quantity = IntegerField('Quantité actuelle', validators=[Optional(), NumberRange(min=0)], default=0)
    description = TextAreaField('Description', validators=[Optional(), Length(max=500)])
    submit = SubmitField('Enregistrer')
