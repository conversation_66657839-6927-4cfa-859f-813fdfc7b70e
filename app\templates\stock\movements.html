{% extends 'base.html' %}

{% block title %}Mouvements de Stock - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-exchange-alt me-2"></i>Mouvements de Stock
    </h1>
    <div>
        <a href="{{ url_for('stock.return_stock') }}" class="btn btn-success me-2">
            <i class="fas fa-undo-alt me-1"></i>Retour au stock
        </a>
        <a href="{{ url_for('stock.remove') }}" class="btn btn-danger me-2">
            <i class="fas fa-arrow-circle-up me-1"></i>Sortie de stock
        </a>
        <a href="{{ url_for('main.products') }}" class="btn btn-info me-2">
            <i class="fas fa-boxes me-1"></i>Gérer Produits & Stock
        </a>
        <a href="{{ url_for('main.stock') }}" class="btn btn-secondary">
            <i class="fas fa-warehouse me-1"></i>État du stock
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Historique des mouvements</h5>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">Filtres</h6>
                    </div>
                    <div class="card-body">
                        <form id="filter-form" class="row g-3">
                            <div class="col-md-3">
                                <label for="filter-type" class="form-label">Type de mouvement</label>
                                <select id="filter-type" class="form-select">
                                    <option value="all">Tous</option>
                                    <option value="IN">Entrées</option>
                                    <option value="OUT">Sorties</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filter-date-from" class="form-label">Date début</label>
                                <input type="date" id="filter-date-from" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label for="filter-date-to" class="form-label">Date fin</label>
                                <input type="date" id="filter-date-to" class="form-control">
                            </div>
                            <div class="col-md-3">
                                <label for="filter-product" class="form-label">Produit</label>
                                <input type="text" id="filter-product" class="form-control" placeholder="Rechercher...">
                            </div>
                            <div class="col-md-12 text-end">
                                <button type="button" id="btn-filter" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>Filtrer
                                </button>
                                <button type="button" id="btn-reset" class="btn btn-secondary">
                                    <i class="fas fa-undo me-1"></i>Réinitialiser
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        {% if movements %}
        <div class="table-responsive">
            <table class="table table-striped table-hover table-bordered" id="movements-table">
                <thead class="table-dark">
                    <tr>
                        <th>Date</th>
                        <th>Produit</th>
                        <th>Référence</th>
                        <th>Type</th>
                        <th>Quantité</th>
                        <th>Document de référence</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in movements %}
                    <tr data-type="{{ movement.movement_type }}" data-date="{{ movement.date.strftime('%Y-%m-%d') }}" data-product="{{ movement.product.name }}">
                        <td>{{ movement.date.strftime('%d/%m/%Y %H:%M') }}</td>
                        <td>{{ movement.product.name }}</td>
                        <td>{{ movement.product.reference }}</td>
                        <td>
                            {% if movement.movement_type == 'IN' %}
                            <span class="badge bg-success">Entrée</span>
                            {% else %}
                            <span class="badge bg-danger">Sortie</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.quantity }}</td>
                        <td>{{ movement.reference_document or '-' }}</td>
                        <td>{{ movement.notes or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <td colspan="4" class="text-end fw-bold">Total des entrées:</td>
                        <td id="total-in">
                            {% set total_in = namespace(value=0) %}
                            {% for movement in movements if movement.movement_type == 'IN' %}
                                {% set total_in.value = total_in.value + movement.quantity %}
                            {% endfor %}
                            <span class="badge bg-success">{{ total_in.value }}</span>
                        </td>
                        <td colspan="2"></td>
                    </tr>
                    <tr>
                        <td colspan="4" class="text-end fw-bold">Total des sorties:</td>
                        <td id="total-out">
                            {% set total_out = namespace(value=0) %}
                            {% for movement in movements if movement.movement_type == 'OUT' %}
                                {% set total_out.value = total_out.value + movement.quantity %}
                            {% endfor %}
                            <span class="badge bg-danger">{{ total_out.value }}</span>
                        </td>
                        <td colspan="2"></td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun mouvement de stock n'a été enregistré.
        </div>
        {% endif %}

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const filterForm = document.getElementById('filter-form');
                const filterType = document.getElementById('filter-type');
                const filterDateFrom = document.getElementById('filter-date-from');
                const filterDateTo = document.getElementById('filter-date-to');
                const filterProduct = document.getElementById('filter-product');
                const btnFilter = document.getElementById('btn-filter');
                const btnReset = document.getElementById('btn-reset');
                const movementsTable = document.getElementById('movements-table');

                if (!movementsTable) return;

                const rows = movementsTable.querySelectorAll('tbody tr');

                btnFilter.addEventListener('click', function() {
                    const type = filterType.value;
                    const dateFrom = filterDateFrom.value ? new Date(filterDateFrom.value) : null;
                    const dateTo = filterDateTo.value ? new Date(filterDateTo.value) : null;
                    const product = filterProduct.value.toLowerCase();

                    rows.forEach(row => {
                        const rowType = row.dataset.type;
                        const rowDate = row.dataset.date ? new Date(row.dataset.date) : null;
                        const rowProduct = row.dataset.product.toLowerCase();

                        let show = true;

                        if (type !== 'all' && rowType !== type) {
                            show = false;
                        }

                        if (dateFrom && rowDate && rowDate < dateFrom) {
                            show = false;
                        }

                        if (dateTo && rowDate && rowDate > dateTo) {
                            show = false;
                        }

                        if (product && !rowProduct.includes(product)) {
                            show = false;
                        }

                        row.style.display = show ? '' : 'none';
                    });

                    updateTotals();
                });

                btnReset.addEventListener('click', function() {
                    filterType.value = 'all';
                    filterDateFrom.value = '';
                    filterDateTo.value = '';
                    filterProduct.value = '';

                    rows.forEach(row => {
                        row.style.display = '';
                    });

                    updateTotals();
                });

                function updateTotals() {
                    const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');

                    let totalIn = 0;
                    let totalOut = 0;

                    visibleRows.forEach(row => {
                        const type = row.dataset.type;
                        const quantityCell = row.querySelector('td:nth-child(5)');
                        const quantity = parseInt(quantityCell.textContent.trim(), 10) || 0;

                        if (type === 'IN') {
                            totalIn += quantity;
                        } else if (type === 'OUT') {
                            totalOut += quantity;
                        }
                    });

                    document.getElementById('total-in').innerHTML = `<span class="badge bg-success">${totalIn}</span>`;
                    document.getElementById('total-out').innerHTML = `<span class="badge bg-danger">${totalOut}</span>`;
                }
            });
        </script>
    </div>
</div>
{% endblock %}
