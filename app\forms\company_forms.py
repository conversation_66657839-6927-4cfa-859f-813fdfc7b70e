from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON>Field, TextAreaField, FileField, SubmitField
from wtforms.validators import DataRequired, Length, Email, Optional

class CompanyForm(FlaskForm):
    name = StringField('Nom de l\'entreprise', validators=[DataRequired(), Length(max=100)])
    logo = FileField('Logo', validators=[Optional()])
    phone = StringField('Téléphone', validators=[Optional(), Length(max=20)])
    email = StringField('Email', validators=[Optional(), Email(), Length(max=120)])
    address = TextAreaField('Adresse', validators=[Optional(), Length(max=200)])
    manager = StringField('Responsable', validators=[Optional(), Length(max=100)])
    
    # Informations fiscales
    tax_id = StringField('IF (Identifiant Fiscal)', validators=[Optional(), Length(max=50)])
    rc = StringField('RC (Registre de Commerce)', validators=[Optional(), Length(max=50)])
    patente = StringField('Patente', validators=[Optional(), Length(max=50)])
    ice = StringField('ICE (Identifiant Commun de l\'Entreprise)', validators=[Optional(), Length(max=50)])
    cnss = StringField('CNSS', validators=[Optional(), Length(max=50)])
    
    # Pied de page personnalisé
    footer_text = TextAreaField('Texte de pied de page', validators=[Optional()])
    
    submit = SubmitField('Enregistrer')
