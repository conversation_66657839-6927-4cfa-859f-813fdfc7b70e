/* Custom styles for Fire Extinguisher Management System */

:root {
    --primary-color: #dc3545;
    --primary-hover: #c82333;
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --gradient-primary: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    --gradient-light: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    --text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer {
    margin-top: auto;
}

/* Changement de la couleur principale (bleu -> rouge) */
.bg-primary {
    background-color: #dc3545 !important;
}

/* Enhanced <PERSON>tons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    border: 2px solid var(--primary-color) !important;
    background: transparent;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
    color: #fff !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #218838);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

/* Unified Delete Button Style - Simple and Working */
.btn-delete, .btn-outline-danger, .btn-delete-modern, .btn-delete-glow,
.btn-delete-neon, .btn-delete-3d, .btn-delete-text {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    border: 2px solid #dc3545 !important;
    color: white !important;
    font-weight: 600;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.btn-delete:hover, .btn-outline-danger:hover, .btn-delete-modern:hover,
.btn-delete-glow:hover, .btn-delete-neon:hover, .btn-delete-3d:hover, .btn-delete-text:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a) !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    border-color: #c82333 !important;
}

.btn-delete:active, .btn-outline-danger:active, .btn-delete-modern:active,
.btn-delete-glow:active, .btn-delete-neon:active, .btn-delete-3d:active, .btn-delete-text:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.3);
}

.btn-delete i, .btn-outline-danger i, .btn-delete-modern i,
.btn-delete-glow i, .btn-delete-neon i, .btn-delete-3d i, .btn-delete-text i {
    font-size: 14px;
    margin-right: 4px;
}

/* Small delete buttons */
.btn-sm.btn-delete, .btn-sm.btn-outline-danger, .btn-sm.btn-delete-modern,
.btn-sm.btn-delete-glow, .btn-sm.btn-delete-neon, .btn-sm.btn-delete-3d, .btn-sm.btn-delete-text {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-sm.btn-delete i, .btn-sm.btn-outline-danger i, .btn-sm.btn-delete-modern i,
.btn-sm.btn-delete-glow i, .btn-sm.btn-delete-neon i, .btn-sm.btn-delete-3d i, .btn-sm.btn-delete-text i {
    font-size: 12px;
    margin-right: 2px;
}

/* Responsive delete buttons */
@media (max-width: 768px) {
    .btn-delete, .btn-outline-danger, .btn-delete-modern,
    .btn-delete-glow, .btn-delete-neon, .btn-delete-3d, .btn-delete-text {
        padding: 6px 8px;
        font-size: 12px;
    }

    .btn-delete i, .btn-outline-danger i, .btn-delete-modern i,
    .btn-delete-glow i, .btn-delete-neon i, .btn-delete-3d i, .btn-delete-text i {
        font-size: 12px;
        margin-right: 2px;
    }
}

/* Accessibility improvements */
.btn-delete:focus, .btn-outline-danger:focus, .btn-delete-modern:focus,
.btn-delete-glow:focus, .btn-delete-neon:focus, .btn-delete-3d:focus, .btn-delete-text:focus {
    outline: 3px solid rgba(255, 71, 87, 0.5);
    outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .btn-delete-neon {
        border-color: #ff4757;
        color: #ff4757;
    }

    .btn-delete-neon:hover {
        background: #ff4757;
        color: #000;
    }
}

.text-primary {
    color: #dc3545 !important;
}

.badge-info {
    background-color: #dc3545 !important;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    color: var(--primary-color) !important;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: #343a40 !important;
    font-weight: 500;
    transition: var(--transition);
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(220, 53, 69, 0.1);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: var(--border-radius);
    z-index: 9999 !important;
    position: absolute !important;
}

/* Fix for all dropdown menus */
.dropdown,
.nav-item.dropdown,
.btn-group {
    position: relative;
    z-index: 1000;
}

.dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 160px;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-menu.show {
    z-index: 9999 !important;
    position: absolute !important;
    display: block !important;
}

/* Ensure navbar dropdowns work */
.navbar .dropdown-menu {
    z-index: 9999 !important;
    position: absolute !important;
}

/* Fix for select elements */
select.form-select,
select.form-control {
    z-index: 1000 !important;
    position: relative !important;
}

/* Fix for cards and containers that might interfere */
.card {
    z-index: 1;
    position: relative;
}

.container,
.container-fluid {
    z-index: 1;
    position: relative;
}

/* Specific fix for maintenance page dropdowns */
.maintenance-filters .dropdown-menu {
    z-index: 10000 !important;
    position: absolute !important;
    background: white !important;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Force all dropdowns to appear above everything - but only when shown */
.dropdown-menu.show,
.nav-item .dropdown-menu.show,
.navbar .dropdown-menu.show,
.btn-group .dropdown-menu.show {
    z-index: 99999 !important;
    position: absolute !important;
    background-color: #ffffff !important;
    border: 1px solid rgba(0,0,0,.15) !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    min-width: 160px !important;
    padding: 0.5rem 0 !important;
    margin: 0.125rem 0 0 !important;
    font-size: 0.875rem !important;
    color: #212529 !important;
    text-align: left !important;
    list-style: none !important;
    background-clip: padding-box !important;
    display: block !important;
}

/* Ensure dropdown items are visible */
.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.25rem 1rem !important;
    clear: both !important;
    font-weight: 400 !important;
    color: #212529 !important;
    text-align: inherit !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
    z-index: 99999 !important;
}

/* Override any conflicting styles */
.card,
.card-body,
.container,
.container-fluid,
.row,
.col,
.table-responsive {
    z-index: auto !important;
}

/* Specific override for navbar */
.navbar {
    z-index: 1030 !important;
}

.navbar .dropdown-menu {
    z-index: 99999 !important;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

/* Card hover effects */
.card {
    transition: var(--transition);
    box-shadow: var(--box-shadow);
    border: none;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-bottom: none;
    font-weight: 600;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
}

/* Table styles */
.table-responsive {
    overflow-x: auto;
}

/* Enhanced Form styles */
.form-control {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
}

.required:after {
    content: " *";
    color: var(--primary-color);
}

/* Enhanced Tables */
.table {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.table-striped tbody tr:nth-of-type(odd) {
    background: rgba(0, 0, 0, 0.02);
}

/* Button styles */
.btn-icon {
    display: inline-flex;
    align-items: center;
}

.btn-icon i {
    margin-right: 0.5rem;
}

/* Status badges */
.badge-draft {
    background-color: #6c757d;
}

.badge-sent {
    background-color: #17a2b8;
}

.badge-accepted {
    background-color: #28a745;
}

.badge-rejected {
    background-color: #dc3545;
}

.badge-paid {
    background-color: #28a745;
}

.badge-overdue {
    background-color: #dc3545;
}

.badge-delivered {
    background-color: #28a745;
}

.badge-returned {
    background-color: #ffc107;
}

/* Logo styles for company */
.logo {
    color: #dc3545;
    font-size: 32px;
    font-weight: bold;
    text-transform: uppercase;
    display: flex;
    align-items: center;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background-color: #dc3545;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.logo-icon span {
    color: white;
    font-size: 24px;
}

/* Permission styles */
.permission-table th,
.permission-table td {
    text-align: center;
}

.permission-yes {
    color: #28a745;
}

.permission-no {
    color: #dc3545;
}

/* Enhanced Form Styling */
.form-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--gradient-light);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.form-section h6 {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
    text-shadow: var(--text-shadow);
}

.form-control-modern {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-control-modern:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
    background: white;
    transform: translateY(-1px);
}

.form-label-modern {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Enhanced Card Styling */
.card-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    transition: var(--transition);
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
}

.card-header-modern {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem;
    border-bottom: none;
    font-weight: 600;
    text-shadow: var(--text-shadow);
}

.card-header-modern h6 {
    margin: 0;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-body-modern {
    padding: 2rem;
}

/* Enhanced Table Styling */
.table-modern {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-modern thead th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border: none;
    padding: 1rem;
    text-shadow: var(--text-shadow);
}

.table-modern tbody tr {
    transition: var(--transition);
}

.table-modern tbody tr:hover {
    background: rgba(220, 53, 69, 0.05);
    transform: scale(1.01);
}

.table-modern tbody td {
    padding: 1rem;
    border-color: rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

/* Enhanced Button Groups */
.btn-group-modern .btn {
    border-radius: 0;
    margin: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group-modern .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.btn-group-modern .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    border-right: none;
}

/* Enhanced Modal Styling */
.modal-content-modern {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 2rem 4rem rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.modal-header-modern {
    background: var(--gradient-primary);
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    padding: 1.5rem;
}

.modal-header-modern h5 {
    font-weight: 600;
    text-shadow: var(--text-shadow);
    margin: 0;
}

.modal-body-modern {
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
}

.modal-footer-modern {
    background: var(--gradient-light);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Enhanced Search Bar */
.search-bar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.search-input-modern {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
}

.search-input-modern:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
    transform: translateY(-1px);
}

/* Enhanced Pagination */
.pagination-modern .page-link {
    border: none;
    border-radius: var(--border-radius);
    margin: 0 2px;
    padding: 0.75rem 1rem;
    color: var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    transition: var(--transition);
}

.pagination-modern .page-link:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.pagination-modern .page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow);
}

/* Enhanced Status Badges */
.badge-modern {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    font-size: 0.75rem;
}

.badge-success-modern {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    text-shadow: var(--text-shadow);
}

.badge-warning-modern {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    text-shadow: var(--text-shadow);
}

.badge-danger-modern {
    background: linear-gradient(135deg, #dc3545, #e83e8c);
    color: white;
    text-shadow: var(--text-shadow);
}

.badge-info-modern {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
    text-shadow: var(--text-shadow);
}

/* Enhanced Alert Styling */
.alert-modern {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    backdrop-filter: blur(10px);
}

/* Force Modal Footer Visibility - Ultimate Fix */
.modal-footer {
    display: flex !important;
    justify-content: flex-end !important;
    padding: 1rem !important;
    border-top: 1px solid #dee2e6 !important;
    background-color: #f8f9fa !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    min-height: 60px !important;
    position: relative !important;
    z-index: 1 !important;
}

.modal-footer .btn {
    margin-left: 0.5rem !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.modal-footer form {
    display: inline-block !important;
    margin: 0 !important;
}

/* Ensure all delete modals work */
.modal[id*="deleteModal"] .modal-footer {
    display: flex !important;
    justify-content: flex-end !important;
    padding: 1rem !important;
    border-top: 1px solid #dee2e6 !important;
    background-color: #f8f9fa !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force visibility for all modal elements */
.modal .modal-dialog .modal-content .modal-footer {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Override any hiding styles */
.modal-footer[style*="display: none"],
.modal-footer[style*="visibility: hidden"],
.modal-footer[style*="opacity: 0"] {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.alert-success-modern {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
    border-left: 4px solid #28a745;
    color: #155724;
}

.alert-danger-modern {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(232, 62, 140, 0.1));
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.alert-warning-modern {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(253, 126, 20, 0.1));
    border-left: 4px solid #ffc107;
    color: #856404;
}

.alert-info-modern {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(111, 66, 193, 0.1));
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .container {
        width: 100%;
        max-width: 100%;
    }

    .card {
        border: none !important;
    }

    .card-header,
    .card-footer {
        background-color: transparent !important;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    thead {
        background-color: #f2f2f2;
    }
}

/* Enhanced Footer */
.footer {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin-top: auto;
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(220, 53, 69, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Alerts */
.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-left: 4px solid #28a745;
    border-radius: var(--border-radius);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-left: 4px solid #dc3545;
    border-radius: var(--border-radius);
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-left: 4px solid #ffc107;
    border-radius: var(--border-radius);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-left: 4px solid #17a2b8;
    border-radius: var(--border-radius);
}

/* Unified Delete Modal Style - Like in the image */
.modal {
    z-index: 1055 !important;
}

.modal-backdrop {
    z-index: 1050 !important;
    background-color: rgba(0, 0, 0, 0.6);
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 1rem);
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

/* Delete Modal Header - Red like in image */
.modal-header.bg-danger, .delete-modal .modal-header {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    color: white !important;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
    padding: 20px 25px;
}

.modal-header.bg-danger .modal-title, .delete-modal .modal-title {
    font-weight: 600;
    font-size: 18px;
    color: white !important;
}

.modal-header.bg-danger .btn-close, .delete-modal .btn-close {
    filter: invert(1);
    opacity: 0.8;
}

.modal-header.bg-danger .btn-close:hover, .delete-modal .btn-close:hover {
    opacity: 1;
}

/* Delete Modal Body */
.delete-modal .modal-body {
    padding: 25px;
    background: white;
}

.delete-modal .modal-body p {
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 1.5;
}

.delete-modal .modal-body .text-danger {
    color: #dc3545 !important;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Force Modal Footer to Show - Critical Fix */
.modal .modal-footer,
.delete-modal .modal-footer,
div[id*="deleteModal"] .modal-footer {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    justify-content: flex-end !important;
    padding: 0.75rem !important;
    border-top: 1px solid #dee2e6 !important;
    border-bottom-right-radius: calc(0.3rem - 1px) !important;
    border-bottom-left-radius: calc(0.3rem - 1px) !important;
    background: #f8f9fa !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    min-height: 50px !important;
    position: relative !important;
    z-index: 1 !important;
}

.modal-footer form,
.delete-modal .modal-footer form {
    display: inline-block !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Force Modal Buttons to Show */
.modal-footer .btn,
.delete-modal .modal-footer .btn,
div[id*="deleteModal"] .modal-footer .btn {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 8px 16px !important;
    margin: 0 5px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    position: relative !important;
    z-index: 2 !important;
    min-width: 80px !important;
    text-align: center !important;
}

.modal-footer .btn-secondary,
.delete-modal .modal-footer .btn-secondary {
    background-color: #6c757d !important;
    border: 1px solid #6c757d !important;
    color: #ffffff !important;
}

.modal-footer .btn-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #5a6268 !important;
    color: #ffffff !important;
}

.delete-modal .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.modal-footer .btn-delete-confirm,
.delete-modal .modal-footer .btn-delete-confirm,
div[id*="deleteModal"] .modal-footer .btn-delete-confirm {
    background-color: #dc3545 !important;
    border: 1px solid #dc3545 !important;
    color: #ffffff !important;
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.modal-footer .btn-delete-confirm:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
    color: #ffffff !important;
}

.delete-modal .btn-danger:hover, .delete-modal .btn-delete-confirm:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Ensure all delete modals work properly */
.modal.delete-modal {
    z-index: 1055 !important;
}

.modal.delete-modal .modal-backdrop {
    z-index: 1050 !important;
}

/* Fix any modal conflicts */
.modal.show {
    display: block !important;
}

.modal-backdrop.show {
    opacity: 0.6 !important;
}

/* Simple Modal Fix */
.modal-dialog-centered {
    display: flex !important;
    align-items: center !important;
    min-height: calc(100% - 1rem) !important;
}

/* Ensure delete buttons are clickable */
.btn-delete, .btn-outline-danger, .btn-delete-modern,
.btn-delete-glow, .btn-delete-neon, .btn-delete-3d, .btn-delete-text {
    cursor: pointer !important;
    pointer-events: auto !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .navbar-nav {
        text-align: center;
    }

    .card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    .modal-dialog {
        margin: 0.5rem;
    }
}
