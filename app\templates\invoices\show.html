{% extends 'base.html' %}

{% block title %}Facture {{ invoice.invoice_number }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice-dollar me-2"></i>Facture {{ invoice.invoice_number }}
    </h1>
    <div>
        <a href="{{ url_for('invoices.generate_pdf', id=invoice.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-file-pdf me-1"></i>Générer PDF
        </a>
        <a href="{{ url_for('invoices.edit', id=invoice.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card mb-4 print-friendly">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5 class="card-title mb-0">Facture N° {{ invoice.invoice_number }}</h5>
                {% if invoice.quote %}
                <small class="text-muted">Basée sur le devis {{ invoice.quote.quote_number }}</small>
                {% endif %}
            </div>
            <div class="col-md-6 text-md-end">
                <span class="badge
                    {% if invoice.status == 'draft' %}bg-secondary
                    {% elif invoice.status == 'sent' %}bg-info
                    {% elif invoice.status == 'paid' %}bg-success
                    {% elif invoice.status == 'overdue' %}bg-danger
                    {% endif %}">
                    {% if invoice.status == 'draft' %}Brouillon
                    {% elif invoice.status == 'sent' %}Envoyée
                    {% elif invoice.status == 'paid' %}Payée
                    {% elif invoice.status == 'overdue' %}En retard
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="fw-bold">Informations de la facture</h6>
                <p>
                    <strong>Date:</strong> {{ invoice.date.strftime('%d/%m/%Y') }}<br>
                    <strong>Date d'échéance:</strong> {{ invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else 'Non spécifiée' }}<br>
                    <strong>Taux de TVA:</strong> {{ invoice.tax_rate }}%
                </p>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">Client</h6>
                <p>
                    <strong>{{ invoice.client.name }}</strong><br>
                    {% if invoice.client.address %}{{ invoice.client.address }}<br>{% endif %}
                    {% if invoice.client.postal_code or invoice.client.city %}
                        {% if invoice.client.postal_code %}{{ invoice.client.postal_code }}{% endif %}
                        {% if invoice.client.city %}{{ invoice.client.city }}{% endif %}<br>
                    {% endif %}
                    {% if invoice.client.email %}Email: {{ invoice.client.email }}<br>{% endif %}
                    {% if invoice.client.phone %}Tél: {{ invoice.client.phone }}{% endif %}
                </p>
            </div>
        </div>

        <h6 class="fw-bold mb-3">Produits</h6>
        {% if invoice.items.all() %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Référence</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td>{{ item.product.name }}</td>
                        <td>{{ item.product.reference }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.unit_price }} MAD</td>
                        <td>{{ item.total }} MAD</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-light">
                        <td colspan="4" class="text-end fw-bold">Sous-total:</td>
                        <td class="fw-bold">{{ invoice.subtotal }} MAD</td>
                    </tr>
                    <tr class="table-light">
                        <td colspan="4" class="text-end fw-bold">TVA ({{ invoice.tax_rate }}%):</td>
                        <td class="fw-bold">{{ invoice.tax_amount }} MAD</td>
                    </tr>
                    <tr class="table-dark">
                        <td colspan="4" class="text-end fw-bold">Total:</td>
                        <td class="fw-bold">{{ invoice.total }} MAD</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun produit n'a été ajouté à cette facture.
            <a href="{{ url_for('invoices.edit', id=invoice.id) }}" class="btn btn-sm btn-success ms-2">
                <i class="fas fa-plus-circle me-1"></i>Ajouter des produits
            </a>
        </div>
        {% endif %}

        {% if invoice.notes %}
        <div class="mt-4">
            <h6 class="fw-bold">Notes</h6>
            <p>{{ invoice.notes }}</p>
        </div>
        {% endif %}
    </div>
    <div class="card-footer">
        <div class="row">
            <div class="col-md-6">
                <small class="text-muted">Créée le {{ invoice.date.strftime('%d/%m/%Y') }}</small>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group no-print">
                    {% if invoice.status == 'draft' %}
                    <form action="#" method="post" class="d-inline">
                        <input type="hidden" name="status" value="sent">
                        <button type="submit" class="btn btn-sm btn-info">
                            <i class="fas fa-paper-plane me-1"></i>Marquer comme envoyée
                        </button>
                    </form>
                    {% elif invoice.status == 'sent' %}
                    <form action="#" method="post" class="d-inline">
                        <input type="hidden" name="status" value="paid">
                        <button type="submit" class="btn btn-sm btn-success">
                            <i class="fas fa-check me-1"></i>Marquer comme payée
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4 no-print">
    <div class="card-header">
        <h5 class="card-title mb-0">Actions</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('invoices.edit', id=invoice.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier la facture
                    </a>
                    <a href="{{ url_for('invoices.generate_pdf', id=invoice.id) }}" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>Générer PDF
                    </a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    {% if invoice.status == 'paid' or invoice.status == 'sent' %}
                    <a href="{{ url_for('delivery_notes.create') }}?invoice_id={{ invoice.id }}" class="btn btn-success">
                        <i class="fas fa-truck me-2"></i>Créer un bon de livraison
                    </a>
                    {% endif %}

                    {% if invoice.items.all() and not invoice.delivery_notes.all() %}
                    <form action="{{ url_for('invoices.create_stock_movement', id=invoice.id) }}" method="post">
                        <button type="submit" class="btn btn-warning d-block w-100">
                            <i class="fas fa-boxes me-2"></i>Créer les mouvements de stock
                        </button>
                    </form>
                    {% endif %}

                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer la facture
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la facture <strong>{{ invoice.invoice_number }}</strong> ?</p>
                <p class="text-danger">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('invoices.delete', id=invoice.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
