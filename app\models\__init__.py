# Import all models to make them available from app.models
from datetime import datetime
from app import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash

# Import models from separate files
from app.models.company import Company
from app.models.user import User

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    product_type = db.Column(db.String(20), nullable=False)  # 'Extincteur' or 'RIA'
    reference = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    unit_price = db.Column(db.Float, nullable=False)
    current_quantity = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    stock_movements = db.relationship('StockMovement', backref='product', lazy='dynamic')
    quote_items = db.relationship('QuoteItem', backref='product', lazy='dynamic')
    invoice_items = db.relationship('InvoiceItem', backref='product', lazy='dynamic')
    delivery_note_items = db.relationship('DeliveryNoteItem', backref='product', lazy='dynamic')

    def __repr__(self):
        return f'<Product {self.name} ({self.reference})>'

class StockMovement(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    movement_type = db.Column(db.String(10), nullable=False)  # 'IN' or 'OUT'
    quantity = db.Column(db.Integer, nullable=False)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    reference_document = db.Column(db.String(50))  # Reference to invoice, delivery note, etc.
    delivery_note_id = db.Column(db.Integer, db.ForeignKey('delivery_note.id'), nullable=True)  # Link to delivery note

    def __repr__(self):
        return f'<StockMovement {self.movement_type} {self.quantity} of {self.product.name}>'

class Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    postal_code = db.Column(db.String(20))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    ice = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    quotes = db.relationship('Quote', backref='client', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='client', lazy='dynamic')
    delivery_notes = db.relationship('DeliveryNote', backref='client', lazy='dynamic')
    equipment_maintenance = db.relationship('EquipmentMaintenance', backref='client', lazy='dynamic')

    def __repr__(self):
        return f'<Client {self.name}>'

class Quote(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    quote_number = db.Column(db.String(20), unique=True, nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    expiration_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='draft')  # draft, sent, accepted, rejected
    subtotal = db.Column(db.Float, default=0.0)
    tax_rate = db.Column(db.Float, default=20.0)  # 20% VAT by default
    tax_amount = db.Column(db.Float, default=0.0)
    total = db.Column(db.Float, default=0.0)
    notes = db.Column(db.Text)

    # Relationships
    items = db.relationship('QuoteItem', backref='quote', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Quote {self.quote_number}>'

    def calculate_totals(self):
        self.subtotal = sum(item.total for item in self.items)
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total = self.subtotal + self.tax_amount

class QuoteItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    quote_id = db.Column(db.Integer, db.ForeignKey('quote.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=True)  # Made optional for custom descriptions
    description = db.Column(db.String(200), nullable=True)  # For custom item descriptions
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)

    def __repr__(self):
        return f'<QuoteItem {self.product.name} x {self.quantity}>'

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, overdue
    subtotal = db.Column(db.Float, default=0.0)
    tax_rate = db.Column(db.Float, default=20.0)  # 20% VAT by default
    tax_amount = db.Column(db.Float, default=0.0)
    total = db.Column(db.Float, default=0.0)
    notes = db.Column(db.Text)
    quote_id = db.Column(db.Integer, db.ForeignKey('quote.id'))  # Optional reference to a quote

    # Relationships
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    delivery_notes = db.relationship('DeliveryNote', backref='invoice', lazy='dynamic')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

    def calculate_totals(self):
        self.subtotal = sum(item.total for item in self.items)
        self.tax_amount = self.subtotal * (self.tax_rate / 100)
        self.total = self.subtotal + self.tax_amount

class InvoiceItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=True)  # Made optional for custom descriptions
    description = db.Column(db.String(200), nullable=True)  # For custom item descriptions
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total = db.Column(db.Float, nullable=False)

    def __repr__(self):
        return f'<InvoiceItem {self.product.name} x {self.quantity}>'

class DeliveryNote(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    delivery_note_number = db.Column(db.String(20), unique=True, nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'))  # Optional reference to an invoice
    date = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='draft')  # draft, delivered, returned
    notes = db.Column(db.Text)

    # Relationships
    items = db.relationship('DeliveryNoteItem', backref='delivery_note', lazy='dynamic', cascade='all, delete-orphan')
    stock_movements = db.relationship('StockMovement', backref='delivery_note', lazy='dynamic')

    def __repr__(self):
        return f'<DeliveryNote {self.delivery_note_number}>'

    def approve_and_update_stock(self):
        """Approve delivery note and update stock automatically"""
        if self.status == 'approved':
            return  # Already approved

        # Update status
        self.status = 'approved'

        # Create stock movements for each item
        for item in self.items:
            if item.product_id and item.quantity > 0:
                # Check if stock movement already exists for this item
                existing_movement = StockMovement.query.filter_by(
                    delivery_note_id=self.id,
                    product_id=item.product_id
                ).first()

                if not existing_movement:
                    # Create stock movement (OUT)
                    movement = StockMovement(
                        product_id=item.product_id,
                        movement_type='OUT',
                        quantity=item.quantity,
                        date=datetime.utcnow(),
                        notes=f'Sortie automatique - Bon de livraison {self.delivery_note_number}',
                        reference_document=self.delivery_note_number,
                        delivery_note_id=self.id
                    )

                    # Update product quantity
                    product = Product.query.get(item.product_id)
                    if product:
                        product.current_quantity = max(0, product.current_quantity - item.quantity)

                    db.session.add(movement)

        db.session.commit()

class DeliveryNoteItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    delivery_note_id = db.Column(db.Integer, db.ForeignKey('delivery_note.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=True)  # Made optional for custom descriptions
    description = db.Column(db.String(200), nullable=True)  # For custom item descriptions
    quantity = db.Column(db.Integer, nullable=False)

    def __repr__(self):
        return f'<DeliveryNoteItem {self.product.name} x {self.quantity}>'


class EquipmentMaintenance(db.Model):
    __tablename__ = 'equipment_maintenance'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)  # Référence au client
    designation = db.Column(db.String(200), nullable=False)
    quantity = db.Column(db.Integer, nullable=False, default=1)
    supply_date = db.Column(db.Date, nullable=False)  # Date de fourniture
    verification_date = db.Column(db.Date, nullable=True)  # Date de vérification

    # Dates de fin selon la situation
    warranty_end_date = db.Column(db.Date, nullable=True)  # Fin de garantie
    recharge_end_date = db.Column(db.Date, nullable=True)  # Fin de recharge
    verification_end_date = db.Column(db.Date, nullable=True)  # Fin de vérification
    replacement_end_date = db.Column(db.Date, nullable=True)  # Fin de changement

    # Situation actuelle
    current_situation = db.Column(db.String(50), nullable=False, default='garantie')  # garantie, recharge, verification, changement

    # Notes
    notes = db.Column(db.Text, nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def get_current_end_date(self):
        """Retourne la date de fin selon la situation actuelle"""
        if self.current_situation == 'garantie':
            return self.warranty_end_date
        elif self.current_situation == 'recharge':
            return self.recharge_end_date
        elif self.current_situation == 'verification':
            return self.verification_end_date
        elif self.current_situation == 'changement':
            return self.replacement_end_date
        return None

    def days_until_end(self):
        """Retourne le nombre de jours jusqu'à la fin de la situation actuelle"""
        end_date = self.get_current_end_date()
        if end_date:
            delta = end_date - datetime.now().date()
            return delta.days
        return None

    def is_expired(self):
        """Vérifie si la date de fin est dépassée"""
        days = self.days_until_end()
        return days is not None and days < 0

    def needs_alert(self):
        """Vérifie si un alerte est nécessaire (moins de 10 jours)"""
        days = self.days_until_end()
        return days is not None and 0 <= days <= 10

    def get_alert_message(self):
        """Retourne le message d'alerte approprié"""
        days = self.days_until_end()
        if days is None:
            return None

        situation_names = {
            'garantie': 'garantie',
            'recharge': 'recharge',
            'verification': 'vérification',
            'changement': 'changement'
        }

        situation = situation_names.get(self.current_situation, self.current_situation)

        if days < 0:
            return f"⚠️ URGENT: La {situation} de '{self.designation}' est expirée depuis {-days} jour(s)!"
        elif days == 0:
            return f"🚨 URGENT: La {situation} de '{self.designation}' expire aujourd'hui!"
        elif days <= 10:
            return f"⚡ ATTENTION: La {situation} de '{self.designation}' expire dans {days} jour(s)!"

        return None

    def __repr__(self):
        return f'<EquipmentMaintenance {self.designation} - {self.client.name if self.client else "No Client"}>'

# Import additional models
from app.models.activity_log import ActivityLog
from app.models.backup import DatabaseBackup, BackupSchedule, ImportLog
