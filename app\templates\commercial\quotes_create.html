{% extends 'base.html' %}

{% block title %}Nouveau Devis - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice me-2"></i>Nouveau Devis
    </h1>
    <div>
        <a href="{{ url_for('main.commercial_quotes') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Informations du devis</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('main.commercial_quotes_create') }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="client_id" class="form-label required">Client</label>
                        {{ form.client_id(class="form-select", required=true) }}
                        {% if form.client_id.errors %}
                            <div class="text-danger">
                                {% for error in form.client_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label required">Statut</label>
                        {{ form.status(class="form-select", required=true) }}
                        {% if form.status.errors %}
                            <div class="text-danger">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label required">Date</label>
                        {{ form.date(class="form-control", type="date") }}
                        {% if form.date.errors %}
                            <div class="text-danger">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="expiration_date" class="form-label">Date d'expiration</label>
                        {{ form.expiration_date(class="form-control", type="date") }}
                        {% if form.expiration_date.errors %}
                            <div class="text-danger">
                                {% for error in form.expiration_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="tax_rate" class="form-label">Taux de TVA (%)</label>
                        {{ form.tax_rate(class="form-control", placeholder="20.0", required=true) }}
                        {% if form.tax_rate.errors %}
                            <div class="text-danger">
                                {% for error in form.tax_rate.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                {{ form.notes(class="form-control", rows=3, placeholder="Notes ou conditions particulières") }}
                {% if form.notes.errors %}
                    <div class="text-danger">
                        {% for error in form.notes.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('main.commercial_quotes') }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<div class="alert alert-info mt-4">
    <i class="fas fa-info-circle me-2"></i>
    <strong>Information:</strong> Après création du devis, vous pourrez ajouter des produits et calculer le montant total.
</div>
{% endblock %}
