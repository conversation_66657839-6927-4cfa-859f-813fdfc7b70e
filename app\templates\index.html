{% extends 'base.html' %}

{% block title %}Tableau de bord - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
        </h1>
        <p class="lead">Bienvenue dans votre application de gestion d'extincteurs et RIA</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Produits</h5>
                        <h2 class="display-6">{{ products_count }}</h2>
                    </div>
                    <i class="fas fa-boxes fa-3x"></i>
                </div>
                <a href="{{ url_for('main.products') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Stock faible</h5>
                        <h2 class="display-6">{{ low_stock_products }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x"></i>
                </div>
                <a href="{{ url_for('main.stock') }}" class="btn btn-light btn-sm mt-2">Voir le stock</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Devis</h5>
                        <h2 class="display-6">{{ quotes_count }}</h2>
                    </div>
                    <i class="fas fa-file-invoice fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_quotes') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Factures</h5>
                        <h2 class="display-6">{{ invoices_count }}</h2>
                    </div>
                    <i class="fas fa-file-invoice-dollar fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_invoices') }}" class="btn btn-light btn-sm mt-2">Voir toutes</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-secondary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Bons de livraison</h5>
                        <h2 class="display-6">{{ delivery_notes_count }}</h2>
                    </div>
                    <i class="fas fa-truck fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_delivery_notes') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>Derniers mouvements de stock
                </h5>
            </div>
            <div class="card-body">
                {% if recent_movements %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Produit</th>
                                <th>Type</th>
                                <th>Quantité</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_movements %}
                            <tr>
                                <td>{{ movement.date.strftime('%d/%m/%Y') }}</td>
                                <td>{{ movement.product.name }}</td>
                                <td>
                                    {% if movement.movement_type == 'IN' %}
                                    <span class="badge bg-success">Entrée</span>
                                    {% else %}
                                    <span class="badge bg-danger">Sortie</span>
                                    {% endif %}
                                </td>
                                <td>{{ movement.quantity }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Aucun mouvement de stock récent.</p>
                {% endif %}
                <a href="{{ url_for('main.stock_movements') }}" class="btn btn-primary btn-sm">Voir tous les mouvements</a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('main.products_create') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus-circle me-2"></i>Nouveau produit
                    </a>
                    <a href="{{ url_for('main.products') }}" class="btn btn-outline-info">
                        <i class="fas fa-boxes me-2"></i>Gérer Produits & Stock
                    </a>
                    <a href="{{ url_for('stock.return_stock') }}" class="btn btn-outline-success">
                        <i class="fas fa-undo-alt me-2"></i>Retour au Stock
                    </a>
                    <a href="{{ url_for('stock.remove') }}" class="btn btn-outline-danger">
                        <i class="fas fa-arrow-circle-up me-2"></i>Sortie de Stock
                    </a>
                    <a href="{{ url_for('main.stock_movements') }}" class="btn btn-outline-warning">
                        <i class="fas fa-exchange-alt me-2"></i>Mouvements de Stock
                    </a>
                    <a href="{{ url_for('quotes.create_interactive') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-file-invoice me-2"></i>Nouveau devis
                    </a>
                    <a href="{{ url_for('invoices.create_interactive') }}" class="btn btn-outline-info">
                        <i class="fas fa-file-invoice-dollar me-2"></i>Nouvelle facture
                    </a>
                    <a href="{{ url_for('delivery_notes.create_interactive') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-truck me-2"></i>Nouveau bon de livraison
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
