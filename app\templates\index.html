{% extends 'base.html' %}

{% block title %}Tableau de bord - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
        </h1>
        <p class="lead">Bienvenue dans votre application de gestion d'extincteurs et RIA</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Produits</h5>
                        <h2 class="display-6">{{ products_count }}</h2>
                    </div>
                    <i class="fas fa-boxes fa-3x"></i>
                </div>
                <a href="{{ url_for('main.products') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Stock faible</h5>
                        <h2 class="display-6">{{ low_stock_products }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x"></i>
                </div>
                <a href="{{ url_for('main.stock') }}" class="btn btn-light btn-sm mt-2">Voir le stock</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Devis</h5>
                        <h2 class="display-6">{{ quotes_count }}</h2>
                    </div>
                    <i class="fas fa-file-invoice fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_quotes') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Factures</h5>
                        <h2 class="display-6">{{ invoices_count }}</h2>
                    </div>
                    <i class="fas fa-file-invoice-dollar fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_invoices') }}" class="btn btn-light btn-sm mt-2">Voir toutes</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-secondary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Bons de livraison</h5>
                        <h2 class="display-6">{{ delivery_notes_count }}</h2>
                    </div>
                    <i class="fas fa-truck fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_delivery_notes') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
</div>


{% endblock %}
