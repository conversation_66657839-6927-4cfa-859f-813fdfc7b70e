{% extends 'base.html' %}

{% block title %}Tableau de bord - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-5">
            <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
        </h1>
        <p class="lead">Bienvenue dans votre application de gestion d'extincteurs et RIA</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Produits</h5>
                        <h2 class="display-6">{{ products_count }}</h2>
                    </div>
                    <i class="fas fa-boxes fa-3x"></i>
                </div>
                <a href="{{ url_for('main.products') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Stock faible</h5>
                        <h2 class="display-6">{{ low_stock_products }}</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x"></i>
                </div>
                <a href="{{ url_for('main.stock') }}" class="btn btn-light btn-sm mt-2">Voir le stock</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Devis</h5>
                        <h2 class="display-6">{{ quotes_count }}</h2>
                    </div>
                    <i class="fas fa-file-invoice fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_quotes') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Factures</h5>
                        <h2 class="display-6">{{ invoices_count }}</h2>
                    </div>
                    <i class="fas fa-file-invoice-dollar fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_invoices') }}" class="btn btn-light btn-sm mt-2">Voir toutes</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-secondary mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Bons de livraison</h5>
                        <h2 class="display-6">{{ delivery_notes_count }}</h2>
                    </div>
                    <i class="fas fa-truck fa-3x"></i>
                </div>
                <a href="{{ url_for('main.commercial_delivery_notes') }}" class="btn btn-light btn-sm mt-2">Voir tous</a>
            </div>
        </div>
    </div>
</div>

<!-- Carte Interactive du Maroc -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marked-alt me-2"></i>Carte Interactive du Maroc - Localisation des Clients et Partenaires
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="moroccoMap" style="height: 500px; width: 100%;"></div>
            </div>
            <div class="card-footer">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-primary me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Clients Actifs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-success me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Distributeurs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-warning me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Prospects</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center justify-content-center">
                            <div class="legend-marker bg-info me-2" style="width: 12px; height: 12px; border-radius: 50%;"></div>
                            <small>Partenaires</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block styles %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
.legend-marker {
    display: inline-block;
    border: 2px solid #fff;
    box-shadow: 0 0 3px rgba(0,0,0,0.3);
}
</style>
{% endblock %}

{% block scripts %}
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the map centered on Morocco
    var map = L.map('moroccoMap').setView([31.7917, -7.0926], 6);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Define custom icons
    var clientIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #dc3545; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    var distributorIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #28a745; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    var prospectIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #ffc107; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    var partnerIcon = L.divIcon({
        className: 'custom-marker',
        html: '<div style="background-color: #17a2b8; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });

    // Sample data for Morocco cities with clients and partners
    var locations = [
        // Clients Actifs
        {
            name: "Société ABC SARL",
            city: "Casablanca",
            type: "client",
            lat: 33.5731,
            lng: -7.5898,
            info: "Client principal - 15 extincteurs en maintenance"
        },
        {
            name: "Hôtel Royal Mansour",
            city: "Marrakech",
            type: "client",
            lat: 31.6295,
            lng: -7.9811,
            info: "Hôtel de luxe - 45 extincteurs et RIA"
        },
        {
            name: "Usine Textile Nord",
            city: "Tanger",
            type: "client",
            lat: 35.7595,
            lng: -5.8340,
            info: "Industrie textile - 80 extincteurs"
        },
        {
            name: "Centre Commercial Mega Mall",
            city: "Rabat",
            type: "client",
            lat: 34.0209,
            lng: -6.8416,
            info: "Centre commercial - 120 extincteurs"
        },
        {
            name: "Port de Mohammedia",
            city: "Mohammedia",
            type: "client",
            lat: 33.6866,
            lng: -7.3674,
            info: "Infrastructure portuaire - 200 extincteurs"
        },

        // Distributeurs
        {
            name: "Distributeur Sécurité Plus",
            city: "Fès",
            type: "distributor",
            lat: 34.0181,
            lng: -5.0078,
            info: "Distributeur régional - Couverture Nord-Est"
        },
        {
            name: "Sécurité Atlas",
            city: "Agadir",
            type: "distributor",
            lat: 30.4278,
            lng: -9.5981,
            info: "Distributeur Sud - Spécialisé hôtellerie"
        },
        {
            name: "Fire Safety Maroc",
            city: "Meknès",
            type: "distributor",
            lat: 33.8935,
            lng: -5.5473,
            info: "Distributeur centre - Formation incluse"
        },

        // Prospects
        {
            name: "Complexe Industriel Kenitra",
            city: "Kenitra",
            type: "prospect",
            lat: 34.2610,
            lng: -6.5802,
            info: "Prospect - Négociation en cours"
        },
        {
            name: "Université Hassan II",
            city: "Casablanca",
            type: "prospect",
            lat: 33.5024,
            lng: -7.6291,
            info: "Prospect éducation - Appel d'offres"
        },
        {
            name: "Aéroport Mohammed V",
            city: "Casablanca",
            type: "prospect",
            lat: 33.3676,
            lng: -7.5897,
            info: "Prospect infrastructure - Étude en cours"
        },

        // Partenaires
        {
            name: "Formation Sécurité Maroc",
            city: "Salé",
            type: "partner",
            lat: 34.0531,
            lng: -6.7985,
            info: "Partenaire formation - Certifications"
        },
        {
            name: "Maintenance Express",
            city: "Oujda",
            type: "partner",
            lat: 34.6814,
            lng: -1.9086,
            info: "Partenaire maintenance - Région orientale"
        },
        {
            name: "Sécurité Sahara",
            city: "Laâyoune",
            type: "partner",
            lat: 27.1253,
            lng: -13.1625,
            info: "Partenaire Sud - Provinces sahariennes"
        }
    ];

    // Add markers to the map
    locations.forEach(function(location) {
        var icon;
        var color;

        switch(location.type) {
            case 'client':
                icon = clientIcon;
                color = '#dc3545';
                break;
            case 'distributor':
                icon = distributorIcon;
                color = '#28a745';
                break;
            case 'prospect':
                icon = prospectIcon;
                color = '#ffc107';
                break;
            case 'partner':
                icon = partnerIcon;
                color = '#17a2b8';
                break;
        }

        var marker = L.marker([location.lat, location.lng], {icon: icon}).addTo(map);

        var popupContent = `
            <div style="min-width: 200px;">
                <h6 style="color: ${color}; margin-bottom: 8px;">
                    <i class="fas fa-map-marker-alt me-1"></i>${location.name}
                </h6>
                <p style="margin-bottom: 5px;">
                    <strong>Ville:</strong> ${location.city}
                </p>
                <p style="margin-bottom: 5px;">
                    <strong>Type:</strong>
                    <span class="badge" style="background-color: ${color};">
                        ${location.type === 'client' ? 'Client' :
                          location.type === 'distributor' ? 'Distributeur' :
                          location.type === 'prospect' ? 'Prospect' : 'Partenaire'}
                    </span>
                </p>
                <p style="margin-bottom: 0; font-size: 0.9em;">
                    ${location.info}
                </p>
            </div>
        `;

        marker.bindPopup(popupContent);
    });

    // Add a scale control
    L.control.scale().addTo(map);

    // Add fullscreen control (optional)
    map.on('click', function() {
        // You can add click events here if needed
    });
});
</script>
{% endblock %}
