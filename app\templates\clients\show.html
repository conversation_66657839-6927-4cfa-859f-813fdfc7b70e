{% extends 'base.html' %}

{% block title %}Client: {{ client.name }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user me-2"></i>{{ client.name }}
    </h1>
    <div>
        <a href="{{ url_for('main.clients') }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('main.clients') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations du client</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Nom:</div>
                    <div class="col-md-8">{{ client.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Adresse:</div>
                    <div class="col-md-8">{{ client.address or 'Non spécifiée' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Code postal:</div>
                    <div class="col-md-8">{{ client.postal_code or 'Non spécifié' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Ville:</div>
                    <div class="col-md-8">{{ client.city or 'Non spécifiée' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Email:</div>
                    <div class="col-md-8">
                        {% if client.email %}
                        <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                        {% else %}
                        Non spécifié
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Téléphone:</div>
                    <div class="col-md-8">
                        {% if client.phone %}
                        <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                        {% else %}
                        Non spécifié
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">ICE:</div>
                    <div class="col-md-8">{{ client.ice or 'Non spécifié' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Date de création:</div>
                    <div class="col-md-8">{{ client.created_at.strftime('%d/%m/%Y') }}</div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('main.clients') }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="clientTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="quotes-tab" data-bs-toggle="tab" data-bs-target="#quotes" type="button" role="tab" aria-controls="quotes" aria-selected="true">Devis</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab" aria-controls="invoices" aria-selected="false">Factures</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="delivery-notes-tab" data-bs-toggle="tab" data-bs-target="#delivery-notes" type="button" role="tab" aria-controls="delivery-notes" aria-selected="false">Bons de livraison</button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="clientTabsContent">
                    <div class="tab-pane fade show active" id="quotes" role="tabpanel" aria-labelledby="quotes-tab">
                        {% if client.quotes.all() %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Numéro</th>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for quote in client.quotes.order_by(Quote.date.desc()).all() %}
                                    <tr>
                                        <td>{{ quote.quote_number }}</td>
                                        <td>{{ quote.date.strftime('%d/%m/%Y') }}</td>
                                        <td>{{ quote.total }} €</td>
                                        <td>
                                            {% if quote.status == 'draft' %}
                                            <span class="badge bg-secondary">Brouillon</span>
                                            {% elif quote.status == 'sent' %}
                                            <span class="badge bg-info">Envoyé</span>
                                            {% elif quote.status == 'accepted' %}
                                            <span class="badge bg-success">Accepté</span>
                                            {% elif quote.status == 'rejected' %}
                                            <span class="badge bg-danger">Refusé</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('main.view_template', template_name='quote') }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucun devis pour ce client.
                        </div>
                        {% endif %}
                        <a href="{{ url_for('main.view_template', template_name='quote') }}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i>Nouveau devis
                        </a>
                    </div>

                    <div class="tab-pane fade" id="invoices" role="tabpanel" aria-labelledby="invoices-tab">
                        {% if client.invoices.all() %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Numéro</th>
                                        <th>Date</th>
                                        <th>Montant</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in client.invoices.order_by(Invoice.date.desc()).all() %}
                                    <tr>
                                        <td>{{ invoice.invoice_number }}</td>
                                        <td>{{ invoice.date.strftime('%d/%m/%Y') }}</td>
                                        <td>{{ invoice.total }} €</td>
                                        <td>
                                            {% if invoice.status == 'draft' %}
                                            <span class="badge bg-secondary">Brouillon</span>
                                            {% elif invoice.status == 'sent' %}
                                            <span class="badge bg-info">Envoyée</span>
                                            {% elif invoice.status == 'paid' %}
                                            <span class="badge bg-success">Payée</span>
                                            {% elif invoice.status == 'overdue' %}
                                            <span class="badge bg-danger">En retard</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('main.view_template', template_name='invoice') }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucune facture pour ce client.
                        </div>
                        {% endif %}
                        <a href="{{ url_for('main.view_template', template_name='invoice') }}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i>Nouvelle facture
                        </a>
                    </div>

                    <div class="tab-pane fade" id="delivery-notes" role="tabpanel" aria-labelledby="delivery-notes-tab">
                        {% if client.delivery_notes.all() %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Numéro</th>
                                        <th>Date</th>
                                        <th>Facture</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for note in client.delivery_notes.order_by(DeliveryNote.date.desc()).all() %}
                                    <tr>
                                        <td>{{ note.delivery_note_number }}</td>
                                        <td>{{ note.date.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            {% if note.invoice %}
                                            <a href="{{ url_for('main.view_template', template_name='invoice') }}">{{ note.invoice.invoice_number }}</a>
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if note.status == 'draft' %}
                                            <span class="badge bg-secondary">Brouillon</span>
                                            {% elif note.status == 'delivered' %}
                                            <span class="badge bg-success">Livré</span>
                                            {% elif note.status == 'returned' %}
                                            <span class="badge bg-warning">Retourné</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('main.view_template', template_name='delivery_note') }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucun bon de livraison pour ce client.
                        </div>
                        {% endif %}
                        <a href="{{ url_for('main.view_template', template_name='delivery_note') }}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-1"></i>Nouveau bon de livraison
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le client <strong>{{ client.name }}</strong> ?</p>
                <p class="text-danger">Cette action est irréversible et supprimera également tous les devis, factures et bons de livraison associés à ce client.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('main.clients') }}" method="post">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
