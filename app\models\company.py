from app import db
from datetime import datetime

class Company(db.Model):
    __tablename__ = 'company'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    logo = db.Column(db.String(255), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    email = db.Column(db.String(120), nullable=True)
    address = db.Column(db.String(200), nullable=True)
    manager = db.Column(db.String(100), nullable=True)
    
    # Informations fiscales
    tax_id = db.Column(db.String(50), nullable=True)  # IF
    rc = db.Column(db.String(50), nullable=True)      # RC
    patente = db.Column(db.String(50), nullable=True) # Patente
    ice = db.Column(db.String(50), nullable=True)     # ICE
    cnss = db.Column(db.String(50), nullable=True)    # CNSS
    
    # Pied de page personnalisé
    footer_text = db.Column(db.Text, nullable=True)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Company {self.name}>'
