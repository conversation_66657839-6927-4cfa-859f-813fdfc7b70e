<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon de livraison N° {{ delivery_note.delivery_note_number if delivery_note else 'BL-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        /* Header with red background */
        .header {
            background-color: #dc3545;
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
            margin-bottom: 0;
        }

        .logo-placeholder {
            position: absolute;
            left: 40px;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            border: 2px dashed white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8pt;
        }

        .document-title {
            font-size: 28pt;
            font-weight: bold;
            margin: 0;
            letter-spacing: 1px;
        }

        /* Content area */
        .content {
            padding: 20px 30px;
            background: white;
        }

        /* Client section */
        .client-section {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
        }

        .client-info {
            flex: 1;
        }

        .delivery-info {
            flex: 1;
            text-align: left;
            margin-left: 50px;
        }

        .info-line {
            margin-bottom: 8px;
            color: #333;
            font-size: 11pt;
        }

        .info-label {
            color: #666;
            margin-right: 10px;
        }

        /* Object section */
        .object-section {
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .object-line {
            border-bottom: 1px solid #dc3545;
            padding-bottom: 5px;
            height: 20px;
        }

        /* Items table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
        }

        .items-table th {
            background: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
        }

        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* Totals section */
        .totals {
            float: right;
            width: 250px;
            margin-top: 30px;
            margin-bottom: 50px;
        }

        .total-line {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 11pt;
        }

        .total-ht {
            border-bottom: 1px solid #ddd;
        }

        .total-tva {
            border-bottom: 1px solid #ddd;
        }

        .total-final {
            font-weight: bold;
            font-size: 12pt;
            color: #dc3545;
            border-bottom: 2px solid #dc3545;
            margin-top: 5px;
            padding-top: 8px;
        }

        /* Signature section */
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            margin-bottom: 50px;
        }

        .signature-box {
            width: 45%;
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            height: 120px;
            display: flex;
            align-items: flex-start;
            justify-content: center;
            padding-top: 15px;
        }

        /* Footer */
        .footer {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #dc3545;
            font-weight: bold;
            padding: 10px 30px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .footer {
                position: static;
                margin-top: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="window.close()">Fermer</button>
    </div>

    <!-- En-tête rouge -->
    <div class="header">
        <div class="logo-placeholder">Logo</div>
        <div class="document-title">Bon de livraison</div>
    </div>

    <!-- Contenu principal -->
    <div class="content">
        <!-- Section Client -->
        <div class="client-section">
            <div class="client-title">Client :</div>
            <div class="client-details">
                <div class="client-info">
                    <div class="info-line">
                        <span class="info-label">Nom du client :</span>
                        {{ delivery_note.client.name if delivery_note and delivery_note.client else 'Nom du client' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Ice :</span>
                        {{ delivery_note.client.ice if delivery_note and delivery_note.client and delivery_note.client.ice else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Téléphone :</span>
                        {{ delivery_note.client.phone if delivery_note and delivery_note.client else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Email :</span>
                        {{ delivery_note.client.email if delivery_note and delivery_note.client else '' }}
                    </div>
                </div>
                <div class="delivery-info">
                    <div class="info-line">
                        <span class="info-label">Date du BL :</span>
                        {{ delivery_note.date.strftime('%d.%m.%Y') if delivery_note and delivery_note.date else '1.6.2021' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Référence du BL :</span>
                        {{ delivery_note.delivery_note_number if delivery_note else '143' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Date de validité :</span>
                        {{ delivery_note.date.strftime('%d.%m.%Y') if delivery_note and delivery_note.date else '15.6.2021' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Objet -->
        <div class="object-section">
            <div class="object-title">Objet :</div>
            <div class="object-line"></div>
        </div>

        <!-- Tableau des articles -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Unité</th>
                    <th>Quantité</th>
                    <th>Prix U.HT</th>
                    <th>Prix T.HT</th>
                </tr>
            </thead>
            <tbody>
                {% if delivery_note and delivery_note.items %}
                    {% for item in delivery_note.items %}
                    <tr>
                        <td style="text-align: left;">{{ item.product.name if item.product else item.description }}</td>
                        <td>{{ item.product.unit if item.product else 'Unité' }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ "%.2f"|format(item.unit_price) }}</td>
                        <td>{{ "%.2f"|format(item.total_price) }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td style="text-align: left;"></td>
                        <td></td>
                        <td></td>
                        <td>0,00</td>
                        <td>0,00</td>
                    </tr>
                    <tr>
                        <td style="text-align: left;"></td>
                        <td></td>
                        <td></td>
                        <td>0,00</td>
                        <td>0,00</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>

        <!-- Totaux -->
        <div class="totals">
            <div class="total-line total-ht">
                <span>Total HT</span>
                <span>{{ "%.2f"|format(delivery_note.subtotal) if delivery_note else '1 123,00' }}</span>
            </div>
            <div class="total-line total-tva">
                <span>Total TVA</span>
                <span>{{ "%.2f"|format(delivery_note.tax_amount) if delivery_note and delivery_note.tax_amount else '224,6' }}</span>
            </div>
            <div class="total-line total-final">
                <span>Total TTC</span>
                <span>{{ "%.2f"|format(delivery_note.total) if delivery_note else '1 347,6' }}</span>
            </div>
        </div>

        <div style="clear: both;"></div>

        <!-- Section des signatures -->
        <div class="signature-section">
            <div class="signature-box">
                Signature et cachet du client
            </div>
            <div class="signature-box">
                Signature et cachet de l'entreprise
            </div>
        </div>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        Pied de page ( Prendre des informations de Pied de page en dans Informations de l'Entreprise )
    </div>
</body>
</html>
