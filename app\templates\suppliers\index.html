{% extends "base.html" %}

{% block title %}Fournisseurs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-truck me-2"></i>Fournisseurs
                </h1>
                <a href="{{ url_for('suppliers.new') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouveau fournisseur
                </a>
            </div>

            <!-- Barre de recherche -->
            <div class="search-bar-modern">
                <form method="GET" class="row g-3">
                    <div class="col-md-10">
                        <input type="text" class="search-input-modern" name="search"
                               placeholder="🔍 Rechercher par nom, email, téléphone ou ville..."
                               value="{{ search }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Rechercher
                        </button>
                    </div>
                </form>
            </div>

            <!-- Liste des fournisseurs -->
            <div class="card-modern">
                <div class="card-header-modern">
                    <h6 class="m-0"><i class="fas fa-list me-2"></i>Liste des fournisseurs</h6>
                </div>
                <div class="card-body-modern">
                    {% if suppliers.items %}
                        <div class="table-responsive">
                            <table class="table-modern table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Contact</th>
                                        <th>Email</th>
                                        <th>Téléphone</th>
                                        <th>Ville</th>
                                        <th>Date création</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for supplier in suppliers.items %}
                                    <tr>
                                        <td>
                                            <strong>{{ supplier.name }}</strong>
                                            {% if supplier.ice %}
                                                <br><small class="text-muted">ICE: {{ supplier.ice }}</small>
                                            {% endif %}
                                        </td>
                                        <td>{{ supplier.contact_person or '-' }}</td>
                                        <td>
                                            {% if supplier.email %}
                                                <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if supplier.phone %}
                                                <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ supplier.city or '-' }}</td>
                                        <td>{{ supplier.created_at.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('suppliers.view', id=supplier.id) }}"
                                                   class="btn btn-sm btn-info" title="Voir">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('suppliers.edit', id=supplier.id) }}"
                                                   class="btn btn-sm btn-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteModal{{ supplier.id }}"
                                                        title="Supprimer">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>

                                            <!-- Modal de confirmation de suppression -->
                                            <div class="modal fade" id="deleteModal{{ supplier.id }}" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Êtes-vous sûr de vouloir supprimer le fournisseur <strong>{{ supplier.name }}</strong> ?</p>
                                                            <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Cette action est irréversible.</p>
                                                        </div>
                                                        <div class="modal-footer" style="display: flex !important; justify-content: flex-end !important; padding: 1rem !important; border-top: 1px solid #dee2e6 !important; background: #f8f9fa !important;">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="margin-right: 10px;">Annuler</button>
                                                            <form action="{{ url_for('suppliers.delete', id=supplier.id) }}" method="post" style="display: inline;">
                                                                <button type="submit" class="btn btn-danger">
                                                                    <i class="fas fa-truck me-2"></i>Supprimer Fournisseur
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if suppliers.pages > 1 %}
                        <nav aria-label="Navigation des pages">
                            <ul class="pagination pagination-modern justify-content-center">
                                {% if suppliers.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('suppliers.index', page=suppliers.prev_num, search=search) }}">Précédent</a>
                                    </li>
                                {% endif %}

                                {% for page_num in suppliers.iter_pages() %}
                                    {% if page_num %}
                                        {% if page_num != suppliers.page %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('suppliers.index', page=page_num, search=search) }}">{{ page_num }}</a>
                                            </li>
                                        {% else %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ page_num }}</span>
                                            </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <span class="page-link">…</span>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if suppliers.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('suppliers.index', page=suppliers.next_num, search=search) }}">Suivant</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucun fournisseur trouvé</h5>
                            {% if search %}
                                <p class="text-muted">Aucun résultat pour "{{ search }}"</p>
                                <a href="{{ url_for('suppliers.index') }}" class="btn btn-outline-primary">Voir tous les fournisseurs</a>
                            {% else %}
                                <p class="text-muted">Commencez par ajouter votre premier fournisseur</p>
                                <a href="{{ url_for('suppliers.new') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Nouveau fournisseur
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
