from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file, session
from datetime import datetime, timedelta
import os
import json
import sqlite3
import zipfile
import tempfile
from werkzeug.utils import secure_filename

bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Decorator to require admin privileges"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Vous devez vous connecter pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        if not session.get('is_admin', False):
            flash('Accès refusé. Privilèges administrateur requis.', 'error')
            return redirect(url_for('main.index'))
        
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/dashboard')
@admin_required
def dashboard():
    """Admin dashboard"""
    from app.models.activity_log import ActivityLog
    from app.models.backup import DatabaseBackup
    from app.models import User
    
    # Get recent activities
    recent_activities = ActivityLog.query.order_by(ActivityLog.timestamp.desc()).limit(10).all()
    
    # Get backup statistics
    total_backups = DatabaseBackup.query.count()
    recent_backups = DatabaseBackup.query.order_by(DatabaseBackup.created_at.desc()).limit(5).all()
    
    # Get user statistics
    total_users = User.query.count()
    active_users = User.query.filter_by(is_active=True).count()
    
    return render_template('admin/dashboard.html',
                         recent_activities=recent_activities,
                         total_backups=total_backups,
                         recent_backups=recent_backups,
                         total_users=total_users,
                         active_users=active_users)

@bp.route('/activity-logs')
@admin_required
def activity_logs():
    """View activity logs"""
    from app.models.activity_log import ActivityLog
    
    page = request.args.get('page', 1, type=int)
    per_page = 50
    
    # Filters
    user_filter = request.args.get('user')
    action_filter = request.args.get('action')
    module_filter = request.args.get('module')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    query = ActivityLog.query
    
    if user_filter:
        query = query.filter(ActivityLog.username.contains(user_filter))
    
    if action_filter:
        query = query.filter(ActivityLog.action == action_filter)
    
    if module_filter:
        query = query.filter(ActivityLog.module == module_filter)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(ActivityLog.timestamp >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            query = query.filter(ActivityLog.timestamp < date_to_obj)
        except ValueError:
            pass
    
    activities = query.order_by(ActivityLog.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get unique values for filters
    unique_users = ActivityLog.query.with_entities(ActivityLog.username).distinct().all()
    unique_actions = ActivityLog.query.with_entities(ActivityLog.action).distinct().all()
    unique_modules = ActivityLog.query.with_entities(ActivityLog.module).filter(
        ActivityLog.module.isnot(None)
    ).distinct().all()
    
    return render_template('admin/activity_logs.html',
                         activities=activities,
                         unique_users=[u[0] for u in unique_users],
                         unique_actions=[a[0] for a in unique_actions],
                         unique_modules=[m[0] for m in unique_modules],
                         filters={
                             'user': user_filter,
                             'action': action_filter,
                             'module': module_filter,
                             'date_from': date_from,
                             'date_to': date_to
                         })

@bp.route('/backups')
@admin_required
def backups():
    """Backup management"""
    from app.models.backup import DatabaseBackup, BackupSchedule
    
    backups = DatabaseBackup.query.order_by(DatabaseBackup.created_at.desc()).all()
    schedules = BackupSchedule.query.all()
    
    return render_template('admin/backups.html', backups=backups, schedules=schedules)

@bp.route('/backup/create', methods=['POST'])
@admin_required
def create_backup():
    """Create manual backup"""
    from app.models.backup import DatabaseBackup
    from app.models.activity_log import ActivityLog
    from app import db
    import shutil
    
    try:
        user_id = session.get('user_id')
        username = session.get('username')
        
        # Create backup directory if it doesn't exist
        backup_dir = os.path.join(os.getcwd(), 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate backup filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, filename)
        
        # Get current database path
        db_path = os.path.join(os.getcwd(), 'app.db')
        
        if os.path.exists(db_path):
            # Copy database file
            shutil.copy2(db_path, backup_path)
            
            # Get file size
            file_size = os.path.getsize(backup_path)
            
            # Create backup record
            backup = DatabaseBackup(
                filename=filename,
                file_path=backup_path,
                file_size=file_size,
                backup_type='manual',
                status='completed',
                created_by=user_id,
                description=f'Sauvegarde manuelle créée par {username}'
            )
            
            db.session.add(backup)
            db.session.commit()
            
            # Log activity
            ActivityLog.log_activity(
                user_id=user_id,
                username=username,
                action='BACKUP_CREATE',
                description=f'Sauvegarde manuelle créée: {filename}',
                module='backup',
                record_id=backup.id,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )
            
            flash(f'Sauvegarde créée avec succès: {filename}', 'success')
        else:
            flash('Fichier de base de données introuvable', 'error')
    
    except Exception as e:
        flash(f'Erreur lors de la création de la sauvegarde: {str(e)}', 'error')
    
    return redirect(url_for('admin.backups'))

@bp.route('/backup/<int:backup_id>/download')
@admin_required
def download_backup(backup_id):
    """Download backup file"""
    from app.models.backup import DatabaseBackup
    from app.models.activity_log import ActivityLog
    
    backup = DatabaseBackup.query.get_or_404(backup_id)
    
    if not backup.file_exists():
        flash('Fichier de sauvegarde introuvable', 'error')
        return redirect(url_for('admin.backups'))
    
    # Log download activity
    ActivityLog.log_activity(
        user_id=session.get('user_id'),
        username=session.get('username'),
        action='BACKUP_DOWNLOAD',
        description=f'Téléchargement de la sauvegarde: {backup.filename}',
        module='backup',
        record_id=backup.id,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )
    
    return send_file(backup.file_path, as_attachment=True, download_name=backup.filename)

@bp.route('/backup/<int:backup_id>/delete', methods=['POST'])
@admin_required
def delete_backup(backup_id):
    """Delete backup"""
    from app.models.backup import DatabaseBackup
    from app.models.activity_log import ActivityLog
    from app import db
    
    backup = DatabaseBackup.query.get_or_404(backup_id)
    
    try:
        # Delete file from disk
        backup.delete_file()
        
        # Log activity before deleting record
        ActivityLog.log_activity(
            user_id=session.get('user_id'),
            username=session.get('username'),
            action='BACKUP_DELETE',
            description=f'Suppression de la sauvegarde: {backup.filename}',
            module='backup',
            record_id=backup.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        # Delete database record
        db.session.delete(backup)
        db.session.commit()
        
        flash('Sauvegarde supprimée avec succès', 'success')
    
    except Exception as e:
        flash(f'Erreur lors de la suppression: {str(e)}', 'error')
    
    return redirect(url_for('admin.backups'))

@bp.route('/export-data')
@admin_required
def export_data():
    """Export all data to JSON"""
    from app.models.activity_log import ActivityLog
    from app import db
    import json
    
    try:
        user_id = session.get('user_id')
        username = session.get('username')
        
        # Create export directory
        export_dir = os.path.join(os.getcwd(), 'exports')
        os.makedirs(export_dir, exist_ok=True)
        
        # Generate export filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'data_export_{timestamp}.json'
        export_path = os.path.join(export_dir, filename)
        
        # Export data (simplified version)
        export_data = {
            'export_info': {
                'timestamp': timestamp,
                'exported_by': username,
                'version': '1.0'
            },
            'data': {
                'message': 'Export functionality will be implemented based on specific requirements'
            }
        }
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        # Log activity
        ActivityLog.log_export(
            user_id=user_id,
            username=username,
            module='all_data',
            description=f'Exportation complète des données: {filename}',
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        return send_file(export_path, as_attachment=True, download_name=filename)
    
    except Exception as e:
        flash(f'Erreur lors de l\'exportation: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))

@bp.route('/import-data', methods=['GET', 'POST'])
@admin_required
def import_data():
    """Import data from file"""
    if request.method == 'POST':
        from app.models.backup import ImportLog
        from app.models.activity_log import ActivityLog
        from app import db
        
        if 'file' not in request.files:
            flash('Aucun fichier sélectionné', 'error')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('Aucun fichier sélectionné', 'error')
            return redirect(request.url)
        
        if file:
            try:
                user_id = session.get('user_id')
                username = session.get('username')
                
                filename = secure_filename(file.filename)
                file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
                
                # Create import log
                import_log = ImportLog(
                    filename=filename,
                    file_type=file_ext,
                    module='manual_import',
                    status='in_progress',
                    imported_by=user_id,
                    description=f'Import manuel par {username}'
                )
                
                db.session.add(import_log)
                db.session.commit()
                
                # Log activity
                ActivityLog.log_import(
                    user_id=user_id,
                    username=username,
                    module='manual_import',
                    description=f'Début d\'importation: {filename}',
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
                
                # Here you would implement the actual import logic
                # For now, just mark as completed
                import_log.status = 'completed'
                import_log.completed_at = datetime.now()
                import_log.total_records = 0
                import_log.imported_records = 0
                
                db.session.commit()
                
                flash('Import terminé avec succès (fonctionnalité en développement)', 'info')
                
            except Exception as e:
                flash(f'Erreur lors de l\'import: {str(e)}', 'error')
    
    return render_template('admin/import_data.html')
