<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number if quote else 'DEMO-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .no-print {
            display: block;
        }

        @media print {
            .no-print {
                display: none !important;
            }
        }
        /* تخصيصات إضافية للعروض */
        .quote-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        .quote-title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }

        .quote-subtitle {
            text-align: center;
            font-size: 12pt;
            opacity: 0.9;
        }

        .validity-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 15px 0;
            border-radius: 5px;
            font-size: 10pt;
            text-align: center;
        }

        .terms-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }

        .terms-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #dc3545;
        }

        .terms-list {
            font-size: 9pt;
            line-height: 1.4;
        }

        .terms-list li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body class="quote-document">
    <!-- أزرار التحكم -->
    <div class="control-buttons no-print">
        <button class="btn btn-print" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-close" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="document-container">
        <!-- ترويسة العرض -->
        <div class="quote-header">
            <div class="quote-title">عرض سعر</div>
            <div class="quote-subtitle">DEVIS / QUOTATION</div>
        </div>

        <!-- معلومات الشركة والعميل -->
        <div class="main-info">
            <div class="info-section">
                <div class="section-title">معلومات الشركة</div>
                {% if company %}
                <div class="company-name">{{ company.name }}</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ company.address }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ company.phone }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">البريد:</span>
                    <span class="info-value">{{ company.email }}</span>
                </div>
                {% if company.ice %}
                <div class="info-item">
                    <span class="info-label">ICE:</span>
                    <span class="info-value">{{ company.ice }}</span>
                </div>
                {% endif %}
                {% else %}
                <div class="company-name">شركة إدارة طفايات الحريق</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">الرباط، المغرب</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">+212 5 37 XX XX XX</span>
                </div>
                {% endif %}
            </div>

            <div class="info-section">
                <div class="section-title">معلومات العميل</div>
                {% if quote.client %}
                <div class="client-name">{{ quote.client.name }}</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ quote.client.address or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المدينة:</span>
                    <span class="info-value">{{ quote.client.city or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ quote.client.phone or '-' }}</span>
                </div>
                {% if quote.client.ice %}
                <div class="info-item">
                    <span class="info-label">ICE:</span>
                    <span class="info-value">{{ quote.client.ice }}</span>
                </div>
                {% endif %}
                {% else %}
                <div class="client-name">عميل غير محدد</div>
                {% endif %}
            </div>

            <div class="info-section">
                <div class="section-title">تفاصيل العرض</div>
                <div class="info-item">
                    <span class="info-label">رقم العرض:</span>
                    <span class="info-value">{{ quote.quote_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">التاريخ:</span>
                    <span class="info-value">{{ quote.date.strftime('%d/%m/%Y') if quote.date else '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">صالح حتى:</span>
                    <span class="info-value">{{ quote.valid_until.strftime('%d/%m/%Y') if quote.valid_until else '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">
                        {% if quote.status == 'draft' %}مسودة
                        {% elif quote.status == 'sent' %}مرسل
                        {% elif quote.status == 'accepted' %}مقبول
                        {% elif quote.status == 'rejected' %}مرفوض
                        {% else %}{{ quote.status }}{% endif %}
                    </span>
                </div>
            </div>
        </div>

        <!-- إشعار صلاحية العرض -->
        {% if quote.valid_until %}
        <div class="validity-notice">
            <strong>تنبيه:</strong> هذا العرض صالح حتى تاريخ {{ quote.valid_until.strftime('%d/%m/%Y') }}
        </div>
        {% endif %}

        <!-- جدول المنتجات -->
        <div class="table-container">
            <table class="professional-table">
                <thead>
                    <tr>
                        <th class="col-number">#</th>
                        <th class="col-description">الوصف</th>
                        <th class="col-unit">الوحدة</th>
                        <th class="col-quantity">الكمية</th>
                        <th class="col-price">السعر الوحدة (درهم)</th>
                        <th class="col-total">المجموع (درهم)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in quote.items %}
                    <tr>
                        <td class="col-number">{{ loop.index }}</td>
                        <td class="col-description">
                            <strong>{{ item.product.name if item.product else 'منتج محذوف' }}</strong>
                            {% if item.product and item.product.reference %}
                            <br><small>المرجع: {{ item.product.reference }}</small>
                            {% endif %}
                            {% if item.product and item.product.description %}
                            <br><small>{{ item.product.description }}</small>
                            {% endif %}
                        </td>
                        <td class="col-unit">قطعة</td>
                        <td class="col-quantity">{{ item.quantity }}</td>
                        <td class="col-price">{{ "%.2f"|format(item.unit_price) }}</td>
                        <td class="col-total">{{ "%.2f"|format(item.total) }}</td>
                    </tr>
                    {% endfor %}

                    <!-- صفوف فارغة للمظهر -->
                    {% for i in range(5 - quote.items|length) %}
                    {% if i >= 0 %}
                    <tr class="empty-row">
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- قسم المجاميع -->
        <div class="totals-section">
            <div class="totals-container">
                <table class="totals-table">
                    <tr>
                        <td class="total-label">المجموع الفرعي:</td>
                        <td class="total-value">{{ "%.2f"|format(quote.subtotal) }} درهم</td>
                    </tr>
                    {% if quote.tax_rate and quote.tax_rate > 0 %}
                    <tr>
                        <td class="total-label">الضريبة ({{ quote.tax_rate }}%):</td>
                        <td class="total-value">{{ "%.2f"|format(quote.tax_amount) }} درهم</td>
                    </tr>
                    {% endif %}
                    {% if quote.discount and quote.discount > 0 %}
                    <tr>
                        <td class="total-label">الخصم:</td>
                        <td class="total-value">-{{ "%.2f"|format(quote.discount) }} درهم</td>
                    </tr>
                    {% endif %}
                    <tr class="final-total">
                        <td class="total-label">المجموع الإجمالي:</td>
                        <td class="total-value">{{ "%.2f"|format(quote.total) }} درهم</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- الشروط والأحكام -->
        <div class="terms-section">
            <div class="terms-title">الشروط والأحكام:</div>
            <ul class="terms-list">
                <li>هذا العرض صالح لمدة 30 يوماً من تاريخ الإصدار</li>
                <li>الأسعار شاملة جميع الضرائب</li>
                <li>التسليم خلال 7-10 أيام عمل من تاريخ تأكيد الطلب</li>
                <li>الدفع: 50% مقدماً والباقي عند التسليم</li>
                <li>ضمان سنة واحدة على جميع المنتجات</li>
                <li>خدمة الصيانة والمتابعة الدورية متوفرة</li>
            </ul>
        </div>

        <!-- قسم التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-title">توقيع العميل</div>
                <div class="signature-line"></div>
                <div class="signature-date">التاريخ: ___________</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">توقيع الشركة</div>
                <div class="signature-line"></div>
                <div class="signature-date">التاريخ: ___________</div>
            </div>
        </div>

        <!-- تذييل الوثيقة -->
        <div class="document-footer">
            <div class="footer-content">
                {% if company and company.footer_text %}
                <p>{{ company.footer_text|nl2br }}</p>
                {% else %}
                <p>شكراً لثقتكم بنا - نحن في خدمتكم دائماً</p>
                <p>للاستفسارات: <EMAIL> | الهاتف: +212 5 37 XX XX XX</p>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
