{% extends 'base.html' %}

{% block title %}Modifier Utilisateur: {{ user.username }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user-edit me-2"></i>Modifier Utilisateur: {{ user.username }}
    </h1>
    <div>
        <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i>Voir
        </a>
        <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Informations de l'utilisateur</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('users.edit', id=user.id) }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="username" class="form-label required">Nom d'utilisateur</label>
                        {{ form.username(class="form-control") }}
                        {% if form.username.errors %}
                            <div class="text-danger">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label required">Email</label>
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Nom complet</label>
                        {{ form.full_name(class="form-control") }}
                        {% if form.full_name.errors %}
                            <div class="text-danger">
                                {% for error in form.full_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="is_admin" class="form-label">Rôle</label>
                        <div class="form-check">
                            {{ form.is_admin(class="form-check-input") }}
                            <label class="form-check-label" for="is_admin">
                                Administrateur
                            </label>
                        </div>
                        <small class="form-text text-muted">Les administrateurs ont accès à toutes les fonctionnalités.</small>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password" class="form-label">Nouveau mot de passe</label>
                        {{ form.password(class="form-control") }}
                        {% if form.password.errors %}
                            <div class="text-danger">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel.</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password2" class="form-label">Confirmer le mot de passe</label>
                        {{ form.password2(class="form-control") }}
                        {% if form.password2.errors %}
                            <div class="text-danger">
                                {% for error in form.password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Permissions d'accès
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> Les administrateurs ont automatiquement accès à toutes les fonctionnalités.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 40%;">
                                        <i class="fas fa-cogs me-2"></i>Module
                                    </th>
                                    <th style="width: 30%;" class="text-center">
                                        <i class="fas fa-eye me-2"></i>Consulter
                                    </th>
                                    <th style="width: 30%;" class="text-center">
                                        <i class="fas fa-edit me-2"></i>Modifier
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <i class="fas fa-box text-primary me-2"></i>
                                        <strong>Produits</strong>
                                        <br><small class="text-muted">Gestion du catalogue produits</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_view_products(class="form-check-input") }}
                                            <label class="form-check-label" for="can_view_products"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_edit_products(class="form-check-input") }}
                                            <label class="form-check-label" for="can_edit_products"></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="fas fa-warehouse text-success me-2"></i>
                                        <strong>Stock</strong>
                                        <br><small class="text-muted">Gestion des stocks et inventaire</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_view_stock(class="form-check-input") }}
                                            <label class="form-check-label" for="can_view_stock"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_edit_stock(class="form-check-input") }}
                                            <label class="form-check-label" for="can_edit_stock"></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="fas fa-users text-info me-2"></i>
                                        <strong>Clients</strong>
                                        <br><small class="text-muted">Gestion de la clientèle</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_view_clients(class="form-check-input") }}
                                            <label class="form-check-label" for="can_view_clients"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_edit_clients(class="form-check-input") }}
                                            <label class="form-check-label" for="can_edit_clients"></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="fas fa-file-invoice text-warning me-2"></i>
                                        <strong>Devis</strong>
                                        <br><small class="text-muted">Création et gestion des devis</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_view_quotes(class="form-check-input") }}
                                            <label class="form-check-label" for="can_view_quotes"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_edit_quotes(class="form-check-input") }}
                                            <label class="form-check-label" for="can_edit_quotes"></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="fas fa-receipt text-danger me-2"></i>
                                        <strong>Factures</strong>
                                        <br><small class="text-muted">Facturation et comptabilité</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_view_invoices(class="form-check-input") }}
                                            <label class="form-check-label" for="can_view_invoices"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_edit_invoices(class="form-check-input") }}
                                            <label class="form-check-label" for="can_edit_invoices"></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="fas fa-truck text-secondary me-2"></i>
                                        <strong>Bons de livraison</strong>
                                        <br><small class="text-muted">Gestion des livraisons</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_view_delivery_notes(class="form-check-input") }}
                                            <label class="form-check-label" for="can_view_delivery_notes"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_edit_delivery_notes(class="form-check-input") }}
                                            <label class="form-check-label" for="can_edit_delivery_notes"></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <i class="fas fa-chart-bar text-dark me-2"></i>
                                        <strong>Rapports</strong>
                                        <br><small class="text-muted">Impression et export de rapports</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="form-check d-inline-block">
                                            {{ form.can_print_reports(class="form-check-input") }}
                                            <label class="form-check-label" for="can_print_reports"></label>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="text-muted">-</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-success btn-sm" onclick="selectAllPermissions()">
                                <i class="fas fa-check-double me-1"></i>Tout sélectionner
                            </button>
                            <button type="button" class="btn btn-warning btn-sm ms-2" onclick="clearAllPermissions()">
                                <i class="fas fa-times me-1"></i>Tout désélectionner
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<script>
// تحديد جميع الصلاحيات
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="can_"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

// إلغاء تحديد جميع الصلاحيات
function clearAllPermissions() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="can_"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// تفعيل/تعطيل الصلاحيات عند تغيير نوع المستخدم
document.addEventListener('DOMContentLoaded', function() {
    const isAdminCheckbox = document.getElementById('is_admin');
    const permissionCheckboxes = document.querySelectorAll('input[type="checkbox"][name^="can_"]');

    function togglePermissions() {
        if (isAdminCheckbox.checked) {
            permissionCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.disabled = true;
            });
        } else {
            permissionCheckboxes.forEach(checkbox => {
                checkbox.disabled = false;
            });
        }
    }

    // تطبيق التغيير عند تحميل الصفحة
    togglePermissions();

    // تطبيق التغيير عند تغيير نوع المستخدم
    isAdminCheckbox.addEventListener('change', togglePermissions);
});
</script>
{% endblock %}
