{% extends 'base.html' %}

{% block title %}Modifier Utilisateur: {{ user.username }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user-edit me-2"></i>Modifier Utilisateur: {{ user.username }}
    </h1>
    <div>
        <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i>Voir
        </a>
        <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Informations de l'utilisateur</h5>
    </div>
    <div class="card-body">
        <form method="post" action="{{ url_for('users.edit', id=user.id) }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="username" class="form-label required">Nom d'utilisateur</label>
                        {{ form.username(class="form-control") }}
                        {% if form.username.errors %}
                            <div class="text-danger">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label required">Email</label>
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Nom complet</label>
                        {{ form.full_name(class="form-control") }}
                        {% if form.full_name.errors %}
                            <div class="text-danger">
                                {% for error in form.full_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="is_admin" class="form-label">Rôle</label>
                        <div class="form-check">
                            {{ form.is_admin(class="form-check-input") }}
                            <label class="form-check-label" for="is_admin">
                                Administrateur
                            </label>
                        </div>
                        <small class="form-text text-muted">Les administrateurs ont accès à toutes les fonctionnalités.</small>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password" class="form-label">Nouveau mot de passe</label>
                        {{ form.password(class="form-control") }}
                        {% if form.password.errors %}
                            <div class="text-danger">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel.</small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password2" class="form-label">Confirmer le mot de passe</label>
                        {{ form.password2(class="form-control") }}
                        {% if form.password2.errors %}
                            <div class="text-danger">
                                {% for error in form.password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Permissions Button -->
            <div class="mb-3">
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#permissionsModal">
                    <i class="fas fa-shield-alt me-2"></i>Permissions
                </button>
            </div>

            <!-- Permissions Modal -->
            <div class="modal fade" id="permissionsModal" tabindex="-1" aria-labelledby="permissionsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="permissionsModalLabel">
                                <i class="fas fa-shield-alt me-2"></i>Permissions
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <h6 class="text-danger">Produits</h6>
                                    <div class="form-check">
                                        {{ form.can_view_products(class="form-check-input") }}
                                        <label class="form-check-label" for="can_view_products">
                                            Voir les produits
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        {{ form.can_edit_products(class="form-check-input") }}
                                        <label class="form-check-label" for="can_edit_products">
                                            Modifier les produits
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <h6 class="text-danger">Stock</h6>
                                    <div class="form-check">
                                        {{ form.can_view_stock(class="form-check-input") }}
                                        <label class="form-check-label" for="can_view_stock">
                                            Voir le stock
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        {{ form.can_edit_stock(class="form-check-input") }}
                                        <label class="form-check-label" for="can_edit_stock">
                                            Modifier le stock
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <h6 class="text-danger">Clients</h6>
                                    <div class="form-check">
                                        {{ form.can_view_clients(class="form-check-input") }}
                                        <label class="form-check-label" for="can_view_clients">
                                            Voir les clients
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        {{ form.can_edit_clients(class="form-check-input") }}
                                        <label class="form-check-label" for="can_edit_clients">
                                            Modifier les clients
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <h6 class="text-danger">Devis</h6>
                                    <div class="form-check">
                                        {{ form.can_view_quotes(class="form-check-input") }}
                                        <label class="form-check-label" for="can_view_quotes">
                                            Voir les devis
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        {{ form.can_edit_quotes(class="form-check-input") }}
                                        <label class="form-check-label" for="can_edit_quotes">
                                            Modifier les devis
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <h6 class="text-danger">Factures</h6>
                                    <div class="form-check">
                                        {{ form.can_view_invoices(class="form-check-input") }}
                                        <label class="form-check-label" for="can_view_invoices">
                                            Voir les factures
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        {{ form.can_edit_invoices(class="form-check-input") }}
                                        <label class="form-check-label" for="can_edit_invoices">
                                            Modifier les factures
                                        </label>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <h6 class="text-danger">Bons de livraison</h6>
                                    <div class="form-check">
                                        {{ form.can_view_delivery_notes(class="form-check-input") }}
                                        <label class="form-check-label" for="can_view_delivery_notes">
                                            Voir les bons de livraison
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        {{ form.can_edit_delivery_notes(class="form-check-input") }}
                                        <label class="form-check-label" for="can_edit_delivery_notes">
                                            Modifier les bons de livraison
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <h6 class="text-danger">Rapports</h6>
                                    <div class="form-check">
                                        {{ form.can_print_reports(class="form-check-input") }}
                                        <label class="form-check-label" for="can_print_reports">
                                            Imprimer les rapports
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Enregistrer</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>

<script>
// تحديد جميع الصلاحيات
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="can_"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

// إلغاء تحديد جميع الصلاحيات
function clearAllPermissions() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name^="can_"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// تفعيل/تعطيل الصلاحيات عند تغيير نوع المستخدم
document.addEventListener('DOMContentLoaded', function() {
    const isAdminCheckbox = document.getElementById('is_admin');
    const permissionCheckboxes = document.querySelectorAll('input[type="checkbox"][name^="can_"]');

    function togglePermissions() {
        if (isAdminCheckbox.checked) {
            permissionCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.disabled = true;
            });
        } else {
            permissionCheckboxes.forEach(checkbox => {
                checkbox.disabled = false;
            });
        }
    }

    // تطبيق التغيير عند تحميل الصفحة
    togglePermissions();

    // تطبيق التغيير عند تغيير نوع المستخدم
    isAdminCheckbox.addEventListener('change', togglePermissions);
});
</script>
{% endblock %}
