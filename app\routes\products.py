from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from app import db
from app.models import Product, StockMovement
from app.forms.product_forms import ProductForm
from datetime import datetime

bp = Blueprint('products', __name__)

@bp.route('/')
def index():
    """List all products"""
    products = Product.query.all()
    return render_template('products/index.html', products=products)

@bp.route('/create', methods=['GET', 'POST'])
def create():
    """Create a new product"""
    form = ProductForm()

    if form.validate_on_submit():
        product = Product(
            name=form.name.data,
            product_type=form.product_type.data,
            reference=form.reference.data,
            unit_price=form.unit_price.data,
            current_quantity=form.current_quantity.data or 0,
            description=form.description.data
        )

        db.session.add(product)
        db.session.commit()

        # Create initial stock movement if quantity > 0
        if product.current_quantity > 0:
            movement = StockMovement(
                product_id=product.id,
                movement_type='in',
                quantity=product.current_quantity,
                reference_document='INIT-STOCK',
                notes='Stock initial lors de la création du produit'
            )
            db.session.add(movement)
            db.session.commit()

        flash('Produit créé avec succès!', 'success')
        return redirect(url_for('products.index'))

    return render_template('products/create.html', form=form)

@bp.route('/<int:id>')
def view(id):
    """View product details"""
    product = Product.query.get_or_404(id)
    try:
        recent_movements = StockMovement.query.filter_by(product_id=id).order_by(StockMovement.date.desc()).limit(10).all()
    except:
        recent_movements = []
    return render_template('products/view.html', product=product, recent_movements=recent_movements)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    """Edit a product"""
    product = Product.query.get_or_404(id)
    form = ProductForm(obj=product)

    if form.validate_on_submit():
        old_quantity = product.current_quantity

        product.name = form.name.data
        product.product_type = form.product_type.data
        product.reference = form.reference.data
        product.unit_price = form.unit_price.data
        product.description = form.description.data

        # Handle quantity change
        new_quantity = form.current_quantity.data or 0
        if new_quantity != old_quantity:
            product.current_quantity = new_quantity

            # Create stock movement for the adjustment
            try:
                quantity_diff = new_quantity - old_quantity
                movement_type = 'in' if quantity_diff > 0 else 'out'
                movement = StockMovement(
                    product_id=product.id,
                    movement_type=movement_type,
                    quantity=abs(quantity_diff),
                    reference_document='ADJUST-STOCK',
                    notes=f'Ajustement de stock lors de la modification du produit'
                )
                db.session.add(movement)
            except:
                pass  # Skip if StockMovement table doesn't exist yet

        db.session.commit()
        flash('Produit modifié avec succès!', 'success')
        return redirect(url_for('products.view', id=product.id))

    return render_template('products/edit.html', form=form, product=product)

@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    """Delete a product"""
    product = Product.query.get_or_404(id)

    # Check if product is used in quotes, invoices, or delivery notes
    try:
        if (hasattr(product, 'quote_items') and product.quote_items.count() > 0 or
            hasattr(product, 'invoice_items') and product.invoice_items.count() > 0 or
            hasattr(product, 'delivery_note_items') and product.delivery_note_items.count() > 0):
            flash('Impossible de supprimer ce produit car il est utilisé dans des documents commerciaux.', 'error')
            return redirect(url_for('products.view', id=id))
    except:
        pass  # Skip if relationships don't exist yet

    # Delete associated stock movements
    try:
        StockMovement.query.filter_by(product_id=id).delete()
    except:
        pass  # Skip if StockMovement table doesn't exist yet

    db.session.delete(product)
    db.session.commit()

    flash('Produit supprimé avec succès!', 'success')
    return redirect(url_for('products.index'))

@bp.route('/low-stock')
def low_stock():
    """List products with low stock"""
    # Products with quantity <= 5 are considered low stock
    products = Product.query.filter(Product.current_quantity <= 5).all()
    return render_template('products/low_stock.html', products=products)

@bp.route('/api/search')
def api_search():
    """API endpoint to search products"""
    query = request.args.get('q', '')
    if len(query) < 2:
        return jsonify([])

    products = Product.query.filter(
        Product.name.contains(query) |
        Product.reference.contains(query)
    ).limit(10).all()

    return jsonify([{
        'id': p.id,
        'name': p.name,
        'reference': p.reference,
        'unit_price': p.unit_price,
        'current_quantity': p.current_quantity
    } for p in products])
