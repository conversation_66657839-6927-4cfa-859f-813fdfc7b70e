from app import db
from datetime import datetime
import os
import json

class DatabaseBackup(db.Model):
    """Model for tracking database backups"""
    __tablename__ = 'database_backups'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.BigInteger)  # Size in bytes
    backup_type = db.Column(db.String(20), nullable=False)  # 'manual', 'automatic'
    status = db.Column(db.String(20), default='completed')  # 'in_progress', 'completed', 'failed'
    created_by = db.Column(db.Integer, db.<PERSON>Key('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    description = db.Column(db.Text)

    # Backup metadata
    tables_count = db.Column(db.Integer)
    records_count = db.Column(db.Integer)
    compression_used = db.Column(db.Boolean, default=False)
    encryption_used = db.Column(db.Boolean, default=False)

    # Relationships - using string reference to avoid circular imports
    # user = db.relationship('User', backref=db.backref('backups', lazy='dynamic'))

    def __repr__(self):
        return f'<DatabaseBackup {self.filename} - {self.status}>'

    @property
    def file_size_human(self):
        """Get human readable file size"""
        if not self.file_size:
            return "Unknown"

        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"

    @property
    def status_icon(self):
        """Get icon for backup status"""
        icons = {
            'in_progress': 'fas fa-spinner fa-spin text-warning',
            'completed': 'fas fa-check-circle text-success',
            'failed': 'fas fa-times-circle text-danger'
        }
        return icons.get(self.status, 'fas fa-question-circle text-muted')

    @property
    def type_icon(self):
        """Get icon for backup type"""
        icons = {
            'manual': 'fas fa-user text-primary',
            'automatic': 'fas fa-robot text-info'
        }
        return icons.get(self.backup_type, 'fas fa-database text-muted')

    def file_exists(self):
        """Check if backup file still exists"""
        return os.path.exists(self.file_path)

    def delete_file(self):
        """Delete the backup file from disk"""
        try:
            if self.file_exists():
                os.remove(self.file_path)
                return True
        except Exception as e:
            print(f"Error deleting backup file: {e}")
        return False

class BackupSchedule(db.Model):
    """Model for automatic backup scheduling"""
    __tablename__ = 'backup_schedules'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    frequency = db.Column(db.String(20), nullable=False)  # 'daily', 'weekly', 'monthly'
    time_of_day = db.Column(db.Time)  # When to run the backup
    day_of_week = db.Column(db.Integer)  # For weekly backups (0=Monday)
    day_of_month = db.Column(db.Integer)  # For monthly backups
    is_active = db.Column(db.Boolean, default=True)

    # Backup settings
    include_logs = db.Column(db.Boolean, default=True)
    compress_backup = db.Column(db.Boolean, default=True)
    max_backups_to_keep = db.Column(db.Integer, default=10)

    # Metadata
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)

    # Relationships - using string reference to avoid circular imports
    # user = db.relationship('User', backref=db.backref('backup_schedules', lazy='dynamic'))

    def __repr__(self):
        return f'<BackupSchedule {self.name} - {self.frequency}>'

    @property
    def frequency_display(self):
        """Get display text for frequency"""
        frequencies = {
            'daily': 'Quotidien',
            'weekly': 'Hebdomadaire',
            'monthly': 'Mensuel'
        }
        return frequencies.get(self.frequency, self.frequency)

    def calculate_next_run(self):
        """Calculate next run time based on frequency"""
        from datetime import datetime, timedelta
        import calendar

        now = datetime.now()

        if self.frequency == 'daily':
            # Next day at specified time
            next_run = now.replace(hour=self.time_of_day.hour,
                                 minute=self.time_of_day.minute,
                                 second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)

        elif self.frequency == 'weekly':
            # Next occurrence of specified day of week
            days_ahead = self.day_of_week - now.weekday()
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            next_run = now + timedelta(days=days_ahead)
            next_run = next_run.replace(hour=self.time_of_day.hour,
                                      minute=self.time_of_day.minute,
                                      second=0, microsecond=0)

        elif self.frequency == 'monthly':
            # Next occurrence of specified day of month
            if now.day < self.day_of_month:
                # This month
                next_run = now.replace(day=self.day_of_month,
                                     hour=self.time_of_day.hour,
                                     minute=self.time_of_day.minute,
                                     second=0, microsecond=0)
            else:
                # Next month
                if now.month == 12:
                    next_year = now.year + 1
                    next_month = 1
                else:
                    next_year = now.year
                    next_month = now.month + 1

                # Handle case where target day doesn't exist in next month
                max_day = calendar.monthrange(next_year, next_month)[1]
                target_day = min(self.day_of_month, max_day)

                next_run = datetime(next_year, next_month, target_day,
                                  self.time_of_day.hour,
                                  self.time_of_day.minute)

        else:
            next_run = now + timedelta(days=1)  # Default to tomorrow

        self.next_run = next_run
        return next_run

class ImportLog(db.Model):
    """Model for tracking data imports"""
    __tablename__ = 'import_logs'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(20), nullable=False)  # 'csv', 'excel', 'json', 'sql'
    module = db.Column(db.String(50), nullable=False)  # 'products', 'clients', etc.
    status = db.Column(db.String(20), default='in_progress')  # 'in_progress', 'completed', 'failed'

    # Import statistics
    total_records = db.Column(db.Integer, default=0)
    imported_records = db.Column(db.Integer, default=0)
    updated_records = db.Column(db.Integer, default=0)
    failed_records = db.Column(db.Integer, default=0)

    # Error tracking
    errors = db.Column(db.Text)  # JSON string of errors
    warnings = db.Column(db.Text)  # JSON string of warnings

    # Metadata
    imported_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    imported_at = db.Column(db.DateTime, default=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    description = db.Column(db.Text)

    # Relationships - using string reference to avoid circular imports
    # user = db.relationship('User', backref=db.backref('imports', lazy='dynamic'))

    def __repr__(self):
        return f'<ImportLog {self.filename} - {self.status}>'

    @property
    def success_rate(self):
        """Calculate import success rate"""
        if self.total_records == 0:
            return 0
        return (self.imported_records / self.total_records) * 100

    @property
    def status_icon(self):
        """Get icon for import status"""
        icons = {
            'in_progress': 'fas fa-spinner fa-spin text-warning',
            'completed': 'fas fa-check-circle text-success',
            'failed': 'fas fa-times-circle text-danger'
        }
        return icons.get(self.status, 'fas fa-question-circle text-muted')

    def get_errors_list(self):
        """Get errors as list"""
        if self.errors:
            try:
                return json.loads(self.errors)
            except:
                return []
        return []

    def get_warnings_list(self):
        """Get warnings as list"""
        if self.warnings:
            try:
                return json.loads(self.warnings)
            except:
                return []
        return []

    def add_error(self, error_message):
        """Add an error to the log"""
        errors = self.get_errors_list()
        errors.append(error_message)
        self.errors = json.dumps(errors)
        self.failed_records += 1

    def add_warning(self, warning_message):
        """Add a warning to the log"""
        warnings = self.get_warnings_list()
        warnings.append(warning_message)
        self.warnings = json.dumps(warnings)
