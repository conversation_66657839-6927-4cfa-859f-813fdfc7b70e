{% extends 'base.html' %}

{% block title %}<PERSON> {{ delivery_note.delivery_note_number }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-truck me-2"></i><PERSON> {{ delivery_note.delivery_note_number }}
    </h1>
    <div>
        <a href="{{ url_for('delivery_notes.generate_pdf', id=delivery_note.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-file-pdf me-1"></i>Générer PDF
        </a>
        <a href="{{ url_for('delivery_notes.edit', id=delivery_note.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('delivery_notes.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card mb-4 print-friendly">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5 class="card-title mb-0">Bon de Livraison N° {{ delivery_note.delivery_note_number }}</h5>
                {% if delivery_note.invoice %}
                <small class="text-muted">Basé sur la facture {{ delivery_note.invoice.invoice_number }}</small>
                {% endif %}
            </div>
            <div class="col-md-6 text-md-end">
                <span class="badge 
                    {% if delivery_note.status == 'draft' %}bg-secondary
                    {% elif delivery_note.status == 'delivered' %}bg-success
                    {% elif delivery_note.status == 'returned' %}bg-warning
                    {% endif %}">
                    {% if delivery_note.status == 'draft' %}Brouillon
                    {% elif delivery_note.status == 'delivered' %}Livré
                    {% elif delivery_note.status == 'returned' %}Retourné
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="fw-bold">Informations du bon de livraison</h6>
                <p>
                    <strong>Date:</strong> {{ delivery_note.date.strftime('%d/%m/%Y') }}<br>
                    {% if delivery_note.invoice %}
                    <strong>Facture associée:</strong> <a href="{{ url_for('invoices.show', id=delivery_note.invoice.id) }}">{{ delivery_note.invoice.invoice_number }}</a><br>
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">Client</h6>
                <p>
                    <strong>{{ delivery_note.client.name }}</strong><br>
                    {% if delivery_note.client.address %}{{ delivery_note.client.address }}<br>{% endif %}
                    {% if delivery_note.client.postal_code or delivery_note.client.city %}
                        {% if delivery_note.client.postal_code %}{{ delivery_note.client.postal_code }}{% endif %}
                        {% if delivery_note.client.city %}{{ delivery_note.client.city }}{% endif %}<br>
                    {% endif %}
                    {% if delivery_note.client.email %}Email: {{ delivery_note.client.email }}<br>{% endif %}
                    {% if delivery_note.client.phone %}Tél: {{ delivery_note.client.phone }}{% endif %}
                </p>
            </div>
        </div>
        
        <h6 class="fw-bold mb-3">Produits</h6>
        {% if delivery_note.items.all() %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Référence</th>
                        <th>Quantité</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in delivery_note.items %}
                    <tr>
                        <td>{{ item.product.name }}</td>
                        <td>{{ item.product.reference }}</td>
                        <td>{{ item.quantity }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun produit n'a été ajouté à ce bon de livraison.
            <a href="{{ url_for('delivery_notes.edit', id=delivery_note.id) }}" class="btn btn-sm btn-success ms-2">
                <i class="fas fa-plus-circle me-1"></i>Ajouter des produits
            </a>
        </div>
        {% endif %}
        
        {% if delivery_note.notes %}
        <div class="mt-4">
            <h6 class="fw-bold">Notes</h6>
            <p>{{ delivery_note.notes }}</p>
        </div>
        {% endif %}
    </div>
    <div class="card-footer">
        <div class="row">
            <div class="col-md-6">
                <small class="text-muted">Créé le {{ delivery_note.date.strftime('%d/%m/%Y') }}</small>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group no-print">
                    {% if delivery_note.status == 'draft' %}
                    <form action="#" method="post" class="d-inline">
                        <input type="hidden" name="status" value="delivered">
                        <button type="submit" class="btn btn-sm btn-success">
                            <i class="fas fa-check me-1"></i>Marquer comme livré
                        </button>
                    </form>
                    {% elif delivery_note.status == 'delivered' %}
                    <form action="#" method="post" class="d-inline">
                        <input type="hidden" name="status" value="returned">
                        <button type="submit" class="btn btn-sm btn-warning">
                            <i class="fas fa-undo me-1"></i>Marquer comme retourné
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4 no-print">
    <div class="card-header">
        <h5 class="card-title mb-0">Actions</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('delivery_notes.edit', id=delivery_note.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier le bon de livraison
                    </a>
                    <a href="{{ url_for('delivery_notes.generate_pdf', id=delivery_note.id) }}" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>Générer PDF
                    </a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer le bon de livraison
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le bon de livraison <strong>{{ delivery_note.delivery_note_number }}</strong> ?</p>
                <p class="text-danger">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('delivery_notes.delete', id=delivery_note.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
