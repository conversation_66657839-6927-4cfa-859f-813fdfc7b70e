{% extends 'base.html' %}

{% block title %}<PERSON> {{ delivery_note.delivery_note_number }} - Gestion d'Extincteurs{% endblock %}

{% block head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<style>
    .document-container {
        width: 210mm;
        min-height: 297mm;
        margin: 0 auto;
        background-color: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .content-wrapper {
        flex: 1;
    }

    /* Header rouge avec logo et titre */
    .header {
        background-color: #dc3545;
        color: white;
        height: 80px;
        display: flex;
        align-items: center;
        padding: 0 30px;
        margin-bottom: 0;
    }

    .logo-container {
        width: 200px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10pt;
        margin-right: 30px;
        overflow: hidden;
    }

    .logo-container img {
        max-width: 200px;
        max-height: 80px;
        object-fit: contain;
    }

    .document-title {
        font-size: 28pt;
        font-weight: bold;
        flex: 1;
        text-align: center;
        margin: 0;
    }

    /* Section Client avec fond gris */
    .client-section {
        background-color: #f8f9fa;
        padding: 15px 30px;
        margin-bottom: 0;
    }

    .client-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
        font-size: 12pt;
    }

    .client-details {
        display: flex;
        justify-content: space-between;
    }

    .client-info, .document-info {
        flex: 1;
    }

    .document-info {
        margin-left: 50px;
    }

    .info-line {
        margin-bottom: 5px;
        font-size: 11pt;
        display: flex;
        align-items: center;
    }

    .info-label {
        color: #666;
        display: inline-block;
        min-width: 120px;
        font-weight: bold;
    }

    .info-value {
        color: #333;
        flex: 1;
        margin-left: 10px;
    }

    /* Section Objet */
    .object-section {
        padding: 15px 30px;
        margin-bottom: 20px;
    }

    .object-title {
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
        font-size: 12pt;
    }

    .object-value {
        border-bottom: 1px solid #dc3545;
        padding: 5px 0;
        color: #333;
        font-size: 11pt;
    }

    /* Tableau des articles - Version simplifiée pour bon de livraison */
    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0 30px;
        width: calc(100% - 60px);
    }

    .items-table th {
        background-color: #dc3545;
        color: white;
        padding: 12px 8px;
        text-align: center;
        font-weight: bold;
        border: 1px solid #dc3545;
        font-size: 11pt;
    }

    .items-table td {
        padding: 8px;
        text-align: center;
        border: 1px solid #ddd;
        font-size: 11pt;
        height: 35px;
    }

    .items-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .items-table tr:nth-child(odd) {
        background-color: white;
    }

    .description-col {
        text-align: left !important;
        padding-left: 15px !important;
    }

    /* Section des signatures */
    .signatures-section {
        margin: 50px 30px 30px 30px;
        display: flex;
        justify-content: space-between;
        gap: 30px;
    }

    .signature-box {
        flex: 1;
        border: 1px solid #ddd;
        height: 120px;
        padding: 10px;
        text-align: center;
        font-weight: bold;
        font-size: 11pt;
    }

    /* Footer */
    .footer {
        margin-top: auto;
        padding: 20px 30px;
        text-align: center;
        font-size: 10pt;
        color: #666;
        border-top: 1px solid #ddd;
        background-color: #f8f9fa;
    }

    /* Boutons d'action */
    .action-buttons {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }

    .btn {
        margin: 3px;
        padding: 8px 15px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-weight: bold;
        text-decoration: none;
        display: inline-block;
    }

    .btn-edit {
        background: #ffc107;
        color: #333;
    }

    .btn-print {
        background: #dc3545;
        color: white;
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
    }

    /* إخفاء عناصر عند الطباعة */
    @media print {
        .action-buttons {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .footer {
            position: static;
            margin-top: 50px;
        }
    }
</style>

<div class="action-buttons">
    <a href="{{ url_for('delivery_notes.edit_interactive', id=delivery_note.id) }}" class="btn btn-edit">
        <i class="fas fa-edit"></i> Modifier
    </a>
    <button onclick="window.print()" class="btn btn-print">
        <i class="fas fa-print"></i> Imprimer
    </button>
    <a href="{{ url_for('delivery_notes.index') }}" class="btn btn-cancel">
        <i class="fas fa-times"></i> Fermer
    </a>
</div>

<div class="document-container">
    <div class="content-wrapper">
        <!-- Header rouge avec logo et titre -->
        <div class="header">
            <div class="logo-container">
                {% if company and company.logo %}
                    <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
                {% else %}
                    Logo
                {% endif %}
            </div>
            <div class="document-title">Bon de livraison</div>
        </div>

        <!-- Section Client avec fond gris -->
        <div class="client-section">
            <div class="client-title">Client :</div>
            <div class="client-details">
                <div class="client-info">
                    <div class="info-line">
                        <span class="info-label">Nom du client :</span>
                        <span class="info-value">{{ delivery_note.client.name }}</span>
                    </div>
                    <div class="info-line">
                        <span class="info-label">Ice :</span>
                        <span class="info-value">{{ delivery_note.client.ice or '-' }}</span>
                    </div>
                    <div class="info-line">
                        <span class="info-label">Téléphone :</span>
                        <span class="info-value">{{ delivery_note.client.phone or '-' }}</span>
                    </div>
                    <div class="info-line">
                        <span class="info-label">Email :</span>
                        <span class="info-value">{{ delivery_note.client.email or '-' }}</span>
                    </div>
                </div>
                <div class="document-info">
                    <div class="info-line">
                        <span class="info-label">Date du BL :</span>
                        <span class="info-value">{{ delivery_note.date.strftime('%d/%m/%Y') }}</span>
                    </div>
                    <div class="info-line">
                        <span class="info-label">Référence du BL :</span>
                        <span class="info-value">{{ delivery_note.delivery_note_number }}</span>
                    </div>
                    <div class="info-line">
                        <span class="info-label">Date de livraison :</span>
                        <span class="info-value">{{ delivery_note.delivery_date.strftime('%d/%m/%Y') if delivery_note.delivery_date else '-' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Objet -->
        <div class="object-section">
            <div class="object-title">Objet :</div>
            <div class="object-value">{{ delivery_note.object or 'Livraison de produits' }}</div>
        </div>

        <!-- Tableau des articles - Version simplifiée pour bon de livraison -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 50%;">Description</th>
                    <th style="width: 25%;">Unité</th>
                    <th style="width: 25%;">Quantité</th>
                </tr>
            </thead>
            <tbody>
                {% for item in delivery_note.items %}
                <tr>
                    <td class="description-col">{{ item.description }}</td>
                    <td>{{ item.unit or 'Unité' }}</td>
                    <td>{{ item.quantity }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Section des signatures -->
        <div class="signatures-section">
            <div class="signature-box">
                <div>Signature et cachet de client</div>
            </div>
            <div class="signature-box">
                <div>Signature et cachet de l'entreprise</div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% elif company %}
            {% if company.name %}{{ company.name }}{% endif %}
            {% if company.address %} - {{ company.address }}{% endif %}
            {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
            {% if company.email %} - Email: {{ company.email }}{% endif %}
            {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
        {% endif %}
    </div>
</div>
{% endblock %}
