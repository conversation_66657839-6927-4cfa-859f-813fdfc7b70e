/**
 * Simple Delete Modal - Guaranteed to Work
 * This is a fallback solution for delete confirmations
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple delete modal script loaded');
    
    // Create a simple modal HTML if it doesn't exist
    createSimpleModal();
    
    // Setup all delete buttons
    setupDeleteButtons();
});

function createSimpleModal() {
    // Check if modal already exists
    if (document.getElementById('simpleDeleteModal')) {
        return;
    }
    
    const modalHTML = `
        <div class="modal fade" id="simpleDeleteModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">Confirmer la suppression</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p id="deleteMessage">Êtes-vous sûr de vouloir supprimer cet élément ?</p>
                        <p class="text-danger">
                            <i class="fas fa-exclamation-triangle"></i> Cette action est irréversible.
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                            <i class="fas fa-trash-alt me-2"></i>Supprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function setupDeleteButtons() {
    // Find all delete buttons
    const deleteButtons = document.querySelectorAll(
        '.btn-delete, .btn-outline-danger, [data-bs-toggle="modal"][data-bs-target*="deleteModal"]'
    );
    
    console.log('Found delete buttons:', deleteButtons.length);
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get the target modal or use simple modal
            const targetModalId = this.getAttribute('data-bs-target');
            const targetModal = targetModalId ? document.querySelector(targetModalId) : null;
            
            if (targetModal && targetModal.querySelector('.modal-footer')) {
                // Original modal exists and has footer, let it work
                return;
            }
            
            // Use simple modal as fallback
            showSimpleDeleteModal(this);
        });
    });
}

function showSimpleDeleteModal(deleteButton) {
    const modal = document.getElementById('simpleDeleteModal');
    const messageElement = document.getElementById('deleteMessage');
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    
    // Customize message based on button context
    let message = 'Êtes-vous sûr de vouloir supprimer cet élément ?';
    
    // Try to find specific item name
    const row = deleteButton.closest('tr');
    if (row) {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) {
            const itemName = cells[1].textContent.trim() || cells[0].textContent.trim();
            if (itemName) {
                message = `Êtes-vous sûr de vouloir supprimer "${itemName}" ?`;
            }
        }
    }
    
    messageElement.textContent = message;
    
    // Setup confirm button
    confirmBtn.onclick = function() {
        // Find the form to submit
        const form = deleteButton.closest('form');
        if (form) {
            form.submit();
        } else {
            // Try to find form by action URL
            const targetModalId = deleteButton.getAttribute('data-bs-target');
            if (targetModalId) {
                const targetModal = document.querySelector(targetModalId);
                if (targetModal) {
                    const modalForm = targetModal.querySelector('form');
                    if (modalForm) {
                        modalForm.submit();
                    }
                }
            }
        }
        
        // Close modal
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }
    };
    
    // Show modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// Export for global use
window.showSimpleDeleteModal = showSimpleDeleteModal;
