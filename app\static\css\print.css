/* ملف CSS للطباعة - تصميم بسيط ونظيف */

/* إعدادات الصفحة للطباعة A4 عمودي */
@page {
    size: A4 portrait;
    margin: 15mm 10mm 15mm 10mm;
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Arial', sans-serif;
    font-size: 12px;
    line-height: 1.4;
    color: #000;
    background: white;
    padding: 20px;
    width: 100%;
    max-width: 210mm; /* عرض A4 عمودي */
}

/* تصميم الرأس - الشعار */
.logo-section {
    margin-bottom: 30px;
}

.logo {
    display: flex;
    align-items: center;
    color: #28a745; /* اللون الأخضر للشعار */
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
}

.logo img {
    max-height: 60px;
    max-width: 200px;
    object-fit: contain;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background-color: #28a745;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 18px;
    font-weight: bold;
    clip-path: polygon(0 0, 100% 0, 85% 100%, 0% 100%);
}

/* قسم المعلومات العلوي */
.info-section {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    gap: 15px;
}

.left-info {
    flex: 1;
    max-width: 55%;
}

.right-info {
    flex: 1;
    max-width: 40%;
    border: 2px solid #000;
    border-radius: 15px;
    padding: 12px;
    text-align: center;
}

.info-row {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
}

.info-label {
    font-weight: bold;
    min-width: 120px;
    margin-right: 10px;
}

.info-value {
    flex: 1;
    border: 1px solid #000;
    padding: 3px 8px;
    min-height: 20px;
}

.right-info h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
}

/* تصميم الجداول */
.table-container {
    margin: 15px 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

th, td {
    border: 1px solid #000;
    padding: 6px 4px;
    text-align: left;
    vertical-align: middle;
    font-size: 10px;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    text-align: center;
}

/* عرض الأعمدة */
.col-num {
    width: 6%;
    text-align: center;
}

.col-designation {
    width: 45%;
}

.col-unit {
    width: 10%;
    text-align: center;
}

.col-qty {
    width: 8%;
    text-align: center;
}

.col-price {
    width: 15%;
    text-align: right;
}

.col-total {
    width: 16%;
    text-align: right;
}

/* أعمدة خاصة ببون التسليم */
.delivery-document .col-designation {
    width: 70%;
}

.delivery-document .col-qty {
    width: 10%;
}

/* صفوف فارغة */
.empty-row td {
    height: 25px;
    border-color: #ddd;
}

/* قسم المجاميع */
.totals-section {
    display: flex;
    justify-content: flex-end;
    margin: 15px 0;
}

.totals-table {
    width: 280px;
}

.totals-table table {
    margin: 0;
    width: 100%;
}

.totals-table th {
    background-color: #f8f9fa;
    text-align: left;
    padding: 8px;
    font-weight: bold;
    width: 60%;
}

.totals-table td {
    text-align: right;
    padding: 8px;
    border: 1px solid #000;
    width: 40%;
}

/* قسم التوقيعات */
.signature-section {
    display: flex;
    justify-content: space-between;
    margin-top: 25px;
    gap: 15px;
}

.signature-box {
    flex: 1;
    border: 1px solid #000;
    height: 100px;
    text-align: center;
    padding: 8px;
    font-weight: bold;
    background-color: #f9f9f9;
}

/* التذييل */
.footer {
    font-size: 8px;
    text-align: center;
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #ddd;
    color: #666;
    line-height: 1.2;
}

.footer p {
    margin: 3px 0;
}

/* إعدادات الطباعة */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    body {
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    /* منع تقسيم العناصر المهمة */
    .info-section, .totals-section, .signature-section {
        page-break-inside: avoid;
    }
    
    .footer {
        page-break-inside: avoid;
    }
}

/* أنماط خاصة للوثائق المختلفة */
.quote-document .document-title h1::after {
    content: " - DEVIS";
}

.invoice-document .document-title h1::after {
    content: " - FACTURE";
}

.delivery-document .document-title h1::after {
    content: " - BON DE LIVRAISON";
}

/* تغيير الألوان الزرقاء إلى حمراء */
.info-value,
.totals-table th,
.totals-table td {
    border-color: #dc3545 !important;
}

/* أنماط خاصة للعناصر التي تحتاج لون أحمر بدلاً من أزرق */
.red-border {
    border-color: #dc3545 !important;
}

.red-bg {
    background-color: #dc3545 !important;
    color: white !important;
}

.red-text {
    color: #dc3545 !important;
}
