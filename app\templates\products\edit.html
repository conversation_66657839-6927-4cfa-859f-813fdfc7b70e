{% extends 'base.html' %}

{% block title %}Modifier Produit - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-edit me-2"></i>Modifier Produit
    </h1>
    <a href="{{ url_for('main.products') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.products_edit', id=product.id) }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label required">Nom du produit</label>
                        {{ form.name(class="form-control", placeholder="Nom du produit") }}
                        {% if form.name.errors %}
                            <div class="text-danger">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="product_type" class="form-label required">Type de produit</label>
                        {{ form.product_type(class="form-select") }}
                        {% if form.product_type.errors %}
                            <div class="text-danger">
                                {% for error in form.product_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="reference" class="form-label required">Référence</label>
                        {{ form.reference(class="form-control", placeholder="Référence unique du produit") }}
                        {% if form.reference.errors %}
                            <div class="text-danger">
                                {% for error in form.reference.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="unit_price" class="form-label required">Prix unitaire (MAD)</label>
                        {{ form.unit_price(class="form-control", placeholder="0.00") }}
                        {% if form.unit_price.errors %}
                            <div class="text-danger">
                                {% for error in form.unit_price.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="current_quantity" class="form-label">Quantité actuelle</label>
                        {{ form.current_quantity(class="form-control", placeholder="0") }}
                        {% if form.current_quantity.errors %}
                            <div class="text-danger">
                                {% for error in form.current_quantity.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Attention: modifier directement la quantité ne créera pas de mouvement de stock</div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                {{ form.description(class="form-control", rows=3, placeholder="Description du produit") }}
                {% if form.description.errors %}
                    <div class="text-danger">
                        {% for error in form.description.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('main.products') }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}
