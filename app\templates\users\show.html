{% extends 'base.html' %}

{% block title %}Utilisateur: {{ user.username }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user me-2"></i>{{ user.username }}
    </h1>
    <div>
        <a href="{{ url_for('users.edit', id=user.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('users.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations de l'utilisateur</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Nom d'utilisateur:</div>
                    <div class="col-md-8">{{ user.username }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Nom complet:</div>
                    <div class="col-md-8">{{ user.full_name or 'Non spécifié' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Email:</div>
                    <div class="col-md-8">
                        <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Rôle:</div>
                    <div class="col-md-8">
                        {% if user.is_admin %}
                        <span class="badge bg-danger">Administrateur</span>
                        {% else %}
                        <span class="badge bg-secondary">Utilisateur</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Créé le:</div>
                    <div class="col-md-8">{{ user.created_at.strftime('%d/%m/%Y') }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Dernière mise à jour:</div>
                    <div class="col-md-8">{{ user.updated_at.strftime('%d/%m/%Y') }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Dernière connexion:</div>
                    <div class="col-md-8">
                        {% if user.last_login %}
                        {{ user.last_login.strftime('%d/%m/%Y %H:%M') }}
                        {% else %}
                        Jamais connecté
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('users.edit', id=user.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Permissions</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Module</th>
                                <th>Voir</th>
                                <th>Modifier</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Produits</td>
                                <td>
                                    {% if user.can_view_products %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.can_edit_products %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td>Stock</td>
                                <td>
                                    {% if user.can_view_stock %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.can_edit_stock %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td>Clients</td>
                                <td>
                                    {% if user.can_view_clients %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.can_edit_clients %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td>Devis</td>
                                <td>
                                    {% if user.can_view_quotes %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.can_edit_quotes %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td>Factures</td>
                                <td>
                                    {% if user.can_view_invoices %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.can_edit_invoices %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td>Bons de livraison</td>
                                <td>
                                    {% if user.can_view_delivery_notes %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.can_edit_delivery_notes %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr>
                                <td>Imprimer les rapports</td>
                                <td colspan="2">
                                    {% if user.can_print_reports %}
                                    <i class="fas fa-check text-success"></i>
                                    {% else %}
                                    <i class="fas fa-times text-danger"></i>
                                    {% endif %}
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong>{{ user.username }}</strong> ?</p>
                <p class="text-danger">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('users.delete', id=user.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
