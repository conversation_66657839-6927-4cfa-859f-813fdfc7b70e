{% extends 'base.html' %}

{% block title %}Nouvelle Facture - Gestion d'Extincteurs{% endblock %}

{% block content %}
<style>
    .document-header {
        background-color: #dc3545;
        color: white;
        padding: 20px 30px;
        margin: -20px -20px 30px -20px;
        text-align: center;
        position: relative;
    }

    .logo-container {
        position: absolute;
        left: 30px;
        top: 50%;
        transform: translateY(-50%);
        width: 60px;
        height: 60px;
        border: 2px dashed white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10pt;
        overflow: hidden;
    }

    .logo-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .document-title {
        font-size: 36pt;
        font-weight: bold;
        margin: 0;
    }

    .form-section {
        background-color: #f8f9fa;
        padding: 20px;
        margin-bottom: 20px;
        border-left: 4px solid #dc3545;
    }

    .section-title {
        color: #dc3545;
        font-weight: bold;
        margin-bottom: 15px;
        font-size: 14pt;
    }

    .btn-primary {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-primary:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
</style>

<div class="document-header">
    <div class="logo-container">
        {% if company and company.logo %}
            <img src="{{ url_for('static', filename='uploads/logos/' + company.logo) }}" alt="Logo">
        {% else %}
            Logo
        {% endif %}
    </div>
    <div class="document-title">Nouvelle Facture</div>
</div>

<div class="d-flex justify-content-end mb-3">
    <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i>Retour à la liste
    </a>
</div>

<div class="form-section">
    <div class="section-title">
        <i class="fas fa-file-invoice-dollar me-2"></i>Informations de la facture
    </div>
        <form method="post" action="{{ url_for('invoices.create') }}">
            {{ form.hidden_tag() }}

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="client_id" class="form-label required">Client</label>
                        {{ form.client_id(class="form-select") }}
                        {% if form.client_id.errors %}
                            <div class="text-danger">
                                {% for error in form.client_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            <a href="{{ url_for('clients.create') }}" target="_blank">
                                <i class="fas fa-plus-circle me-1"></i>Ajouter un nouveau client
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="quote_id" class="form-label">Basé sur un devis</label>
                        {{ form.quote_id(class="form-select") }}
                        {% if form.quote_id.errors %}
                            <div class="text-danger">
                                {% for error in form.quote_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Si sélectionné, les produits du devis seront automatiquement ajoutés à la facture</div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="status" class="form-label required">Statut</label>
                        {{ form.status(class="form-select") }}
                        {% if form.status.errors %}
                            <div class="text-danger">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">Date</label>
                        {{ form.date(class="form-control", type="date") }}
                        {% if form.date.errors %}
                            <div class="text-danger">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Si non spécifiée, la date actuelle sera utilisée</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="due_date" class="form-label">Date d'échéance</label>
                        {{ form.due_date(class="form-control", type="date") }}
                        {% if form.due_date.errors %}
                            <div class="text-danger">
                                {% for error in form.due_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Si non spécifiée, la date sera fixée à 30 jours après la date de la facture</div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="tax_rate" class="form-label required">Taux de TVA (%)</label>
                        {{ form.tax_rate(class="form-control") }}
                        {% if form.tax_rate.errors %}
                            <div class="text-danger">
                                {% for error in form.tax_rate.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                {{ form.notes(class="form-control", rows=3, placeholder="Informations complémentaires, conditions de paiement...") }}
                {% if form.notes.errors %}
                    <div class="text-danger">
                        {% for error in form.notes.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>Après avoir créé la facture, vous pourrez ajouter des produits si vous n'avez pas sélectionné de devis.
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary me-md-2">Annuler</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
</div>

<script>
// إضافة معلومات الشركة للنموذج
document.addEventListener('DOMContentLoaded', function() {
    // يمكن إضافة JavaScript هنا لتحسين التفاعل
});
</script>
{% endblock %}
