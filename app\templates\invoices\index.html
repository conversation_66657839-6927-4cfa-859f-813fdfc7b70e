{% extends 'base.html' %}

{% block title %}Factures - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice-dollar me-2"></i>Factures
    </h1>
    <a href="{{ url_for('invoices.create_interactive') }}" class="btn btn-primary">
        <i class="fas fa-plus-circle me-1"></i>Nouvelle facture
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if invoices %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Numéro</th>
                        <th>Client</th>
                        <th>Date</th>
                        <th>Échéance</th>
                        <th>Montant</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>{{ invoice.invoice_number }}</td>
                        <td>{{ invoice.client.name }}</td>
                        <td>{{ invoice.date.strftime('%d/%m/%Y') }}</td>
                        <td>{{ invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else '-' }}</td>
                        <td>{{ invoice.total }} MAD</td>
                        <td>
                            {% if invoice.status == 'draft' %}
                            <span class="badge bg-secondary">Brouillon</span>
                            {% elif invoice.status == 'sent' %}
                            <span class="badge bg-info">Envoyée</span>
                            {% elif invoice.status == 'paid' %}
                            <span class="badge bg-success">Payée</span>
                            {% elif invoice.status == 'overdue' %}
                            <span class="badge bg-danger">En retard</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('invoices.show', id=invoice.id) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="Voir">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('invoices.edit_interactive', id=invoice.id) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('invoices.generate_pdf', id=invoice.id) }}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Générer PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-delete" data-bs-toggle="modal" data-bs-target="#deleteModal{{ invoice.id }}" data-bs-toggle="tooltip" title="Supprimer la facture">
                                    <i class="fas fa-receipt"></i>
                                </button>
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal fade delete-modal" id="deleteModal{{ invoice.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header bg-danger">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer la facture <strong>{{ invoice.invoice_number }}</strong> ?</p>
                                            <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Cette action est irréversible.</p>
                                        </div>
                                        <div class="modal-footer" style="display: flex !important; justify-content: flex-end !important; padding: 1rem !important; border-top: 1px solid #dee2e6 !important; background: #f8f9fa !important;">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="margin-right: 10px;">Annuler</button>
                                            <form action="{{ url_for('invoices.delete', id=invoice.id) }}" method="post" style="display: inline;">
                                                <button type="submit" class="btn btn-danger">
                                                    <i class="fas fa-file-times me-2"></i>Supprimer Facture
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucune facture n'a été créée.
            <a href="{{ url_for('invoices.create') }}">Créer une facture</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
