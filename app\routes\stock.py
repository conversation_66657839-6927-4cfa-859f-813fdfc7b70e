from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from app import db
from app.models import Product, StockMovement
from app.forms.stock_forms import StockMovementForm
from datetime import datetime

bp = Blueprint('stock', __name__)

@bp.route('/')
def index():
    """List all stock movements"""
    movements = StockMovement.query.order_by(StockMovement.date.desc()).all()
    return render_template('stock/index.html', movements=movements)

@bp.route('/products')
def products():
    """List all products with stock info"""
    products = Product.query.all()
    return render_template('stock/products.html', products=products)

# Redirect old /add route to new /return route
@bp.route('/add')
def add():
    """Redirect old add route to new return route"""
    return redirect(url_for('stock.return_stock'))

@bp.route('/return', methods=['GET', 'POST'])
def return_stock():
    """Return stock (stock return from customer)"""
    form = StockMovementForm()
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        product = Product.query.get(form.product_id.data)

        # Create stock movement
        movement = StockMovement(
            product_id=form.product_id.data,
            movement_type='return',
            quantity=form.quantity.data,
            reference_document=form.reference_document.data,
            notes=form.notes.data
        )

        # Update product quantity
        product.current_quantity += form.quantity.data

        db.session.add(movement)
        db.session.commit()

        flash(f'Retour au stock enregistré: +{form.quantity.data} {product.name}', 'success')
        return redirect(url_for('stock.index'))

    return render_template('stock/return.html', form=form)

@bp.route('/remove', methods=['GET', 'POST'])
def remove():
    """Remove stock (stock out)"""
    form = StockMovementForm()
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference}) - Stock: {p.current_quantity}") for p in Product.query.all()]

    if form.validate_on_submit():
        product = Product.query.get(form.product_id.data)

        # Check if enough stock available
        if product.current_quantity < form.quantity.data:
            flash(f'Stock insuffisant! Stock disponible: {product.current_quantity}', 'error')
            return render_template('stock/remove.html', form=form)

        # Create stock movement
        movement = StockMovement(
            product_id=form.product_id.data,
            movement_type='out',
            quantity=form.quantity.data,
            reference_document=form.reference_document.data,
            notes=form.notes.data
        )

        # Update product quantity
        product.current_quantity -= form.quantity.data

        db.session.add(movement)
        db.session.commit()

        flash(f'Sortie de stock enregistrée: -{form.quantity.data} {product.name}', 'success')
        return redirect(url_for('stock.index'))

    return render_template('stock/remove.html', form=form)

@bp.route('/movements/<int:product_id>')
def product_movements(product_id):
    """View all movements for a specific product"""
    product = Product.query.get_or_404(product_id)
    movements = StockMovement.query.filter_by(product_id=product_id).order_by(StockMovement.date.desc()).all()
    return render_template('stock/product_movements.html', product=product, movements=movements)

@bp.route('/low-stock')
def low_stock():
    """List products with low stock"""
    # Products with quantity <= 5 are considered low stock
    products = Product.query.filter(Product.current_quantity <= 5).all()
    return render_template('stock/low_stock.html', products=products)

@bp.route('/report')
def report():
    """Stock report"""
    products = Product.query.all()

    # Calculate stock values
    total_value = sum(p.current_quantity * p.unit_price for p in products)
    low_stock_count = len([p for p in products if p.current_quantity <= 5])
    out_of_stock_count = len([p for p in products if p.current_quantity == 0])

    return render_template('stock/report.html',
                         products=products,
                         total_value=total_value,
                         low_stock_count=low_stock_count,
                         out_of_stock_count=out_of_stock_count)

@bp.route('/api/product-stock/<int:product_id>')
def api_product_stock(product_id):
    """API endpoint to get current stock for a product"""
    product = Product.query.get_or_404(product_id)
    return jsonify({
        'id': product.id,
        'name': product.name,
        'reference': product.reference,
        'current_quantity': product.current_quantity,
        'unit_price': product.unit_price
    })
