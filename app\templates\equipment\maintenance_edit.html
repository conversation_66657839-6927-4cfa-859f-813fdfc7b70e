{% extends "base.html" %}

{% block title %}Modifier Équipement - {{ equipment.designation }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-edit text-danger me-2"></i>Modifier Équipement
            </h1>
            <p class="text-muted">{{ equipment.designation }}</p>
        </div>
        <a href="{{ url_for('main.equipment_maintenance') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Retour à la liste
        </a>
    </div>

    <!-- Status Alert -->
    <div class="row mb-4">
        <div class="col-12">
            {% set alert_message = equipment.get_alert_message() %}
            {% if alert_message %}
                <div class="alert {% if equipment.is_expired() %}alert-danger{% else %}alert-warning{% endif %} alert-dismissible fade show" role="alert">
                    <strong>{{ alert_message }}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-fire-extinguisher me-2"></i>Informations de l'Équipement
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Client Information -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.client_id.label(class="form-label required") }}
                                    {{ form.client_id(class="form-select", required=true) }}
                                    {% if form.client_id.errors %}
                                        <div class="text-danger">
                                            {% for error in form.client_id.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Basic Information -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.designation.label(class="form-label required") }}
                                    {{ form.designation(class="form-control", required=true) }}
                                    {% if form.designation.errors %}
                                        <div class="text-danger">
                                            {% for error in form.designation.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    {{ form.quantity.label(class="form-label required") }}
                                    {{ form.quantity(class="form-control", required=true) }}
                                    {% if form.quantity.errors %}
                                        <div class="text-danger">
                                            {% for error in form.quantity.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Dates -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.supply_date.label(class="form-label required") }}
                                    {{ form.supply_date(class="form-control", required=true) }}
                                    {% if form.supply_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.supply_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.verification_date.label(class="form-label") }}
                                    {{ form.verification_date(class="form-control") }}
                                    {% if form.verification_date.errors %}
                                        <div class="text-danger">
                                            {% for error in form.verification_date.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Current Situation -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.current_situation.label(class="form-label required") }}
                                    {{ form.current_situation(class="form-select", required=true) }}
                                    {% if form.current_situation.errors %}
                                        <div class="text-danger">
                                            {% for error in form.current_situation.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- End Dates Section -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-secondary">
                                    <i class="fas fa-calendar-alt me-2"></i>Dates de Fin de Situation
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.warranty_end_date.label(class="form-label") }}
                                            {{ form.warranty_end_date(class="form-control") }}
                                            {% if form.warranty_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.warranty_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.recharge_end_date.label(class="form-label") }}
                                            {{ form.recharge_end_date(class="form-control") }}
                                            {% if form.recharge_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.recharge_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.verification_end_date.label(class="form-label") }}
                                            {{ form.verification_end_date(class="form-control") }}
                                            {% if form.verification_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.verification_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.replacement_end_date.label(class="form-label") }}
                                            {{ form.replacement_end_date(class="form-control") }}
                                            {% if form.replacement_end_date.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.replacement_end_date.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    {{ form.notes.label(class="form-label") }}
                                    {{ form.notes(class="form-control") }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger">
                                            {% for error in form.notes.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('main.equipment_maintenance') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            {{ form.submit(class="btn btn-danger") }}
                        </div>
                    </form>
                </div>
            </div>

            <!-- Equipment History Card -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-history me-2"></i>Historique de l'Équipement
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Créé le:</strong> {{ equipment.created_at.strftime('%d/%m/%Y à %H:%M') if equipment.created_at else '-' }}</p>
                            <p><strong>Dernière modification:</strong> {{ equipment.updated_at.strftime('%d/%m/%Y à %H:%M') if equipment.updated_at else '-' }}</p>
                        </div>
                        <div class="col-md-6">
                            {% set days_until_end = equipment.days_until_end() %}
                            {% if days_until_end is not none %}
                                <p><strong>Jours restants:</strong>
                                    <span class="{% if days_until_end < 0 %}text-danger{% elif days_until_end <= 10 %}text-warning{% else %}text-success{% endif %}">
                                        {% if days_until_end < 0 %}
                                            Expiré depuis {{ (days_until_end * -1) }} jour(s)
                                        {% else %}
                                            {{ days_until_end }} jour(s)
                                        {% endif %}
                                    </span>
                                </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Highlight current end date based on situation
    const currentSituation = document.getElementById('current_situation');
    const endDateInputs = {
        'garantie': document.getElementById('warranty_end_date'),
        'recharge': document.getElementById('recharge_end_date'),
        'verification': document.getElementById('verification_end_date'),
        'changement': document.getElementById('replacement_end_date')
    };

    function highlightCurrentEndDate() {
        // Remove all highlights
        Object.values(endDateInputs).forEach(input => {
            if (input) {
                input.parentElement.classList.remove('border', 'border-primary', 'rounded', 'p-2', 'bg-light');
            }
        });

        // Highlight current situation's end date
        const currentInput = endDateInputs[currentSituation.value];
        if (currentInput) {
            currentInput.parentElement.classList.add('border', 'border-primary', 'rounded', 'p-2', 'bg-light');
        }
    }

    currentSituation.addEventListener('change', highlightCurrentEndDate);
    highlightCurrentEndDate(); // Initial highlight
});
</script>
{% endblock %}
