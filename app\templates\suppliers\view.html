{% extends "base.html" %}

{% block title %}{{ supplier.name }} - Fournisseur{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-truck me-2"></i>{{ supplier.name }}
                </h1>
                <div>
                    <a href="{{ url_for('suppliers.edit', id=supplier.id) }}" class="btn btn-warning me-2">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <a href="{{ url_for('suppliers.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informations gén<PERSON>les -->
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>Informations générales
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted" style="width: 40%;">Nom :</td>
                                    <td>{{ supplier.name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">Personne de contact :</td>
                                    <td>{{ supplier.contact_person or '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">Email :</td>
                                    <td>
                                        {% if supplier.email %}
                                            <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">Téléphone :</td>
                                    <td>
                                        {% if supplier.phone %}
                                            <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">Date de création :</td>
                                    <td>{{ supplier.created_at.strftime('%d/%m/%Y à %H:%M') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Adresse -->
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-map-marker-alt me-2"></i>Adresse
                            </h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold text-muted" style="width: 40%;">Adresse :</td>
                                    <td>{{ supplier.address or '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">Ville :</td>
                                    <td>{{ supplier.city or '-' }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold text-muted">Code postal :</td>
                                    <td>{{ supplier.postal_code or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations fiscales -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-file-invoice me-2"></i>Informations fiscales
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">Identifiant Fiscal (IF) :</td>
                                            <td>{{ supplier.tax_id or '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-4">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">Registre de Commerce (RC) :</td>
                                            <td>{{ supplier.rc or '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-4">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">ICE :</td>
                                            <td>{{ supplier.ice or '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-cogs me-2"></i>Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex gap-2">
                                <a href="{{ url_for('suppliers.edit', id=supplier.id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>Modifier ce fournisseur
                                </a>
                                <button type="button" class="btn btn-danger"
                                        onclick="confirmDelete('{{ supplier.name }}', '{{ url_for('suppliers.delete', id=supplier.id) }}', 'le fournisseur')">
                                    <i class="fas fa-trash me-2"></i>Supprimer ce fournisseur
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}
