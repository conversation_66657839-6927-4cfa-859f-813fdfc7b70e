/**
 * OPTIMIZED DELETE MODAL HANDLER - CLEAN AND SIMPLE
 */

// Global delete confirmation function
function confirmDelete(itemId, deleteUrl, itemType = 'élément') {
    // Update modal content
    const modal = document.getElementById('globalDeleteModal');
    const itemNameElement = document.getElementById('deleteItemName');
    const deleteForm = document.getElementById('globalDeleteForm');
    const itemTypeElement = document.getElementById('deleteItemType');

    if (modal && itemNameElement && deleteForm) {
        // Update displayed item name
        itemNameElement.textContent = itemId;

        // Update item type
        if (itemTypeElement) {
            itemTypeElement.textContent = itemType;
        }

        // Update form action
        deleteForm.action = deleteUrl;

        // Show modal using Bootstrap
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    } else {
        // Fallback to confirm dialog
        if (confirm(`Êtes-vous sûr de vouloir supprimer ${itemType} "${itemId}" ? Cette action est irréversible.`)) {
            // Create and submit form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = deleteUrl;
            document.body.appendChild(form);
            form.submit();
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Optimized delete handler loaded');

    // Create global modal if it doesn't exist
    createGlobalDeleteModal();

    // Setup existing delete buttons (fallback)
    setupExistingDeleteButtons();
});

function createGlobalDeleteModal() {
    // Check if modal already exists
    if (document.getElementById('globalDeleteModal')) {
        return;
    }

    // Create modal HTML
    const modalHTML = `
        <div id="globalDeleteModal" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content border-danger">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-exclamation-triangle me-2"></i>Confirmer la suppression
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                        <p>Êtes-vous sûr de vouloir supprimer <span id="deleteItemType">l'élément</span> <strong class="text-danger" id="deleteItemName"></strong> ?</p>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>Cette action est irréversible.
                        </div>
                    </div>
                    <div class="modal-footer justify-content-center">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annuler
                        </button>
                        <form method="POST" id="globalDeleteForm" style="display: inline;">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Supprimer
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function setupExistingDeleteButtons() {
    // Handle existing delete buttons as fallback
    const deleteButtons = document.querySelectorAll('[data-bs-toggle="modal"][data-bs-target*="deleteModal"]');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Try to extract info from button
            const targetModal = this.getAttribute('data-bs-target');
            const modalElement = document.querySelector(targetModal);

            if (modalElement) {
                // Show the existing modal
                const bsModal = new bootstrap.Modal(modalElement);
                bsModal.show();
            }
        });
    });
}
