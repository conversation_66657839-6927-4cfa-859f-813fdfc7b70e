/**
 * Unified Delete Modal Handler
 * Ensures all delete buttons and modals work consistently across the application
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Delete modals script loaded');
    
    // Initialize all delete modals
    initializeDeleteModals();
    
    // Setup delete button handlers
    setupDeleteButtons();
    
    // Fix modal backdrop issues
    fixModalBackdrops();
});

function initializeDeleteModals() {
    const deleteModals = document.querySelectorAll('.delete-modal, [id*="deleteModal"]');
    console.log('Found delete modals:', deleteModals.length);
    
    deleteModals.forEach(modal => {
        try {
            // Ensure modal has proper classes
            if (!modal.classList.contains('delete-modal')) {
                modal.classList.add('delete-modal');
            }
            
            // Initialize Bootstrap modal if not already done
            if (!bootstrap.Modal.getInstance(modal)) {
                new bootstrap.Modal(modal, {
                    backdrop: 'static',
                    keyboard: true,
                    focus: true
                });
                console.log('Initialized modal:', modal.id);
            }
        } catch (error) {
            console.error('Error initializing modal:', modal.id, error);
        }
    });
}

function setupDeleteButtons() {
    const deleteButtons = document.querySelectorAll(
        '.btn-delete, .btn-outline-danger, .btn-delete-modern, ' +
        '.btn-delete-glow, .btn-delete-neon, .btn-delete-3d, .btn-delete-text, ' +
        '[data-bs-toggle="modal"][data-bs-target*="deleteModal"]'
    );
    
    console.log('Found delete buttons:', deleteButtons.length);
    
    deleteButtons.forEach(button => {
        // Remove existing listeners to prevent duplicates
        button.removeEventListener('click', handleDeleteButtonClick);
        
        // Add unified click handler
        button.addEventListener('click', handleDeleteButtonClick);
        
        // Ensure button is properly styled
        if (!button.classList.contains('btn-delete')) {
            button.classList.add('btn-delete');
        }
    });
}

function handleDeleteButtonClick(e) {
    const button = e.currentTarget;
    const targetModalId = button.getAttribute('data-bs-target');
    
    console.log('Delete button clicked:', button, 'Target modal:', targetModalId);
    
    if (targetModalId) {
        e.preventDefault();
        
        const targetModal = document.querySelector(targetModalId);
        if (targetModal) {
            try {
                // Get or create modal instance
                let modalInstance = bootstrap.Modal.getInstance(targetModal);
                if (!modalInstance) {
                    modalInstance = new bootstrap.Modal(targetModal, {
                        backdrop: 'static',
                        keyboard: true,
                        focus: true
                    });
                }
                
                // Show the modal
                modalInstance.show();
                console.log('Modal shown:', targetModalId);
                
            } catch (error) {
                console.error('Error showing modal:', targetModalId, error);
                
                // Fallback to confirm dialog
                const confirmMessage = 'Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.';
                if (confirm(confirmMessage)) {
                    // Find and submit the form if it exists
                    const form = button.closest('form');
                    if (form) {
                        form.submit();
                    }
                }
            }
        } else {
            console.error('Target modal not found:', targetModalId);
            
            // Fallback to confirm dialog
            const confirmMessage = 'Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.';
            if (confirm(confirmMessage)) {
                const form = button.closest('form');
                if (form) {
                    form.submit();
                }
            }
        }
    } else {
        // Button without modal target - use confirm dialog
        const confirmMessage = 'Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.';
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }
    }
}

function fixModalBackdrops() {
    // Fix modal backdrop z-index issues
    document.addEventListener('show.bs.modal', function(e) {
        const modal = e.target;
        if (modal.classList.contains('delete-modal')) {
            modal.style.zIndex = '1055';
            
            // Fix backdrop
            setTimeout(() => {
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.style.zIndex = '1050';
                }
            }, 10);
        }
    });
    
    // Clean up on modal hide
    document.addEventListener('hidden.bs.modal', function(e) {
        const modal = e.target;
        if (modal.classList.contains('delete-modal')) {
            // Remove any leftover backdrops
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                if (!document.querySelector('.modal.show')) {
                    backdrop.remove();
                }
            });
        }
    });
}

// Export functions for use in other scripts
window.deleteModalHandler = {
    initializeDeleteModals,
    setupDeleteButtons,
    handleDeleteButtonClick,
    fixModalBackdrops
};

console.log('Delete modal handler ready');
