from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app import db
from app.models import Supplier
from app.forms.supplier_forms import SupplierForm
from datetime import datetime

suppliers_bp = Blueprint('suppliers', __name__)

@suppliers_bp.route('/suppliers')
def index():
    """Liste des fournisseurs"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Supplier.query
    
    if search:
        query = query.filter(
            Supplier.name.contains(search) |
            Supplier.email.contains(search) |
            Supplier.phone.contains(search) |
            Supplier.city.contains(search)
        )
    
    suppliers = query.order_by(Supplier.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('suppliers/index.html', suppliers=suppliers, search=search)

@suppliers_bp.route('/suppliers/new', methods=['GET', 'POST'])
def new():
    """Créer un nouveau fournisseur"""
    form = SupplierForm()
    
    if form.validate_on_submit():
        supplier = Supplier(
            name=form.name.data,
            contact_person=form.contact_person.data,
            email=form.email.data,
            phone=form.phone.data,
            address=form.address.data,
            city=form.city.data,
            postal_code=form.postal_code.data,
            tax_id=form.tax_id.data,
            rc=form.rc.data,
            ice=form.ice.data
        )
        
        try:
            db.session.add(supplier)
            db.session.commit()
            flash('Fournisseur créé avec succès!', 'success')
            return redirect(url_for('suppliers.index'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création du fournisseur.', 'error')
    
    return render_template('suppliers/new.html', form=form)

@suppliers_bp.route('/suppliers/<int:id>')
def view(id):
    """Voir les détails d'un fournisseur"""
    supplier = Supplier.query.get_or_404(id)
    return render_template('suppliers/view.html', supplier=supplier)

@suppliers_bp.route('/suppliers/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    """Modifier un fournisseur"""
    supplier = Supplier.query.get_or_404(id)
    form = SupplierForm(obj=supplier)
    
    if form.validate_on_submit():
        supplier.name = form.name.data
        supplier.contact_person = form.contact_person.data
        supplier.email = form.email.data
        supplier.phone = form.phone.data
        supplier.address = form.address.data
        supplier.city = form.city.data
        supplier.postal_code = form.postal_code.data
        supplier.tax_id = form.tax_id.data
        supplier.rc = form.rc.data
        supplier.ice = form.ice.data
        
        try:
            db.session.commit()
            flash('Fournisseur modifié avec succès!', 'success')
            return redirect(url_for('suppliers.view', id=supplier.id))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la modification du fournisseur.', 'error')
    
    return render_template('suppliers/edit.html', form=form, supplier=supplier)

@suppliers_bp.route('/suppliers/<int:id>/delete', methods=['POST'])
def delete(id):
    """Supprimer un fournisseur"""
    supplier = Supplier.query.get_or_404(id)
    
    try:
        db.session.delete(supplier)
        db.session.commit()
        flash('Fournisseur supprimé avec succès!', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression du fournisseur.', 'error')
    
    return redirect(url_for('suppliers.index'))

@suppliers_bp.route('/api/suppliers')
def api_suppliers():
    """API pour récupérer la liste des fournisseurs (pour les sélecteurs)"""
    suppliers = Supplier.query.order_by(Supplier.name).all()
    return jsonify([{
        'id': supplier.id,
        'name': supplier.name,
        'email': supplier.email,
        'phone': supplier.phone
    } for supplier in suppliers])
