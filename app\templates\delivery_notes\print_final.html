<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon de livraison N° {{ delivery_note.delivery_number if delivery_note else '143' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 0;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Header rouge avec logo et titre */
        .header {
            background-color: #dc3545;
            color: white;
            height: 80px;
            display: flex;
            align-items: center;
            padding: 0 30px;
            margin-bottom: 0;
        }

        .logo-container {
            width: 200px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            margin-right: 30px;
            overflow: hidden;
        }

        .logo-container img {
            max-width: 200px;
            max-height: 80px;
            object-fit: contain;
        }

        .document-title {
            font-size: 28pt;
            font-weight: bold;
            flex: 1;
            text-align: center;
            margin: 0;
        }

        /* Section Client avec fond gris */
        .client-section {
            background-color: #f8f9fa;
            padding: 15px 30px;
            margin-bottom: 0;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
        }

        .client-info {
            flex: 1;
        }

        .document-info {
            flex: 1;
            text-align: left;
            margin-left: 50px;
        }

        .info-line {
            margin-bottom: 5px;
            font-size: 11pt;
        }

        .info-label {
            color: #666;
            display: inline-block;
            min-width: 120px;
        }

        /* Section Objet */
        .object-section {
            padding: 15px 30px;
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 12pt;
        }

        .object-content {
            font-size: 11pt;
            color: #333;
            min-height: 20px;
            padding: 5px 0;
            border-bottom: 1px solid #dc3545;
        }

        /* Tableau des articles - Version simplifiée pour bon de livraison */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 30px;
            width: calc(100% - 60px);
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
            height: 30px;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .items-table tr:nth-child(odd) {
            background-color: white;
        }

        .description-col {
            text-align: left !important;
            padding-left: 15px !important;
        }

        /* Section des signatures */
        .signatures-section {
            margin: 50px 30px 30px 30px;
            display: flex;
            justify-content: space-between;
            gap: 30px;
        }

        .signature-box {
            flex: 1;
            border: 1px solid #ddd;
            height: 120px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 11pt;
        }

        /* Footer */
        .footer {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #dc3545;
            font-weight: bold;
            padding: 10px 30px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            html {
                margin: 0 !important;
                padding: 0 !important;
                width: 210mm !important;
                height: 297mm !important;
            }

            body {
                background: white !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 11pt !important;
                width: 210mm !important;
                height: 297mm !important;
                transform: scale(1) !important;
                transform-origin: 0 0 !important;
            }

            .page-container {
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0mm !important;
                width: 210mm !important;
                height: 297mm !important;
                max-width: 210mm !important;
                min-height: 297mm !important;
                box-sizing: border-box !important;
                position: relative !important;
                overflow: hidden !important;
            }

            /* Preserve colors in print */
            .header {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .client-section {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Reduce footer font size */
            .footer {
                font-size: 8pt !important;
                margin-top: 20px !important;
                padding: 10px !important;
                position: static;
            }

            /* Compact layout for single page */
            .header {
                padding: 8px 20px !important;
                margin-bottom: 10px !important;
            }

            .client-section {
                padding: 8px 15px !important;
                margin-bottom: 10px !important;
            }

            .object-section {
                margin-bottom: 10px !important;
            }

            .items-table {
                font-size: 10pt !important;
                margin-bottom: 10px !important;
            }

            .signatures-section {
                margin-bottom: 10px !important;
            }

            /* Force single page */
            @page {
                size: A4 portrait;
                margin: 0mm;
            }

            .page-container {
                page-break-inside: avoid !important;
                page-break-after: avoid !important;
                page-break-before: avoid !important;
            }

            /* Full page layout for print - no margins */
            .header {
                height: 60px !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .client-section {
                padding: 10px !important;
                margin: 0 !important;
            }

            .object-section {
                padding: 10px !important;
                margin: 0 !important;
                margin-bottom: 10px !important;
            }

            .items-table {
                margin: 0 !important;
                width: 100% !important;
                font-size: 9pt !important;
            }

            .signatures-section {
                margin: 10px 0 !important;
                padding: 0 10px !important;
            }

            .footer {
                padding: 8px !important;
                font-size: 7pt !important;
                margin: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="history.back()">Fermer</button>

        {% if delivery_note %}
            {% if delivery_note.status == 'draft' %}
                <form method="POST" action="{{ url_for('delivery_notes.approve', id=delivery_note.id) }}" style="display: inline;">
                    <button type="submit" class="btn" style="background: #28a745; color: white;"
                            onclick="return confirm('Êtes-vous sûr de vouloir approuver ce bon de livraison? Le stock sera automatiquement mis à jour.')">
                        ✓ Approuver et Mettre à Jour Stock
                    </button>
                </form>
            {% elif delivery_note.status == 'approved' %}
                <span class="btn" style="background: #28a745; color: white; cursor: default;">✓ Approuvé</span>
                <form method="POST" action="{{ url_for('delivery_notes.cancel_approval', id=delivery_note.id) }}" style="display: inline;">
                    <button type="submit" class="btn" style="background: #ffc107; color: black;"
                            onclick="return confirm('Êtes-vous sûr de vouloir annuler l\'approbation? Le stock sera restauré.')">
                        ↶ Annuler Approbation
                    </button>
                </form>
            {% endif %}
        {% endif %}
    </div>

    <div class="page-container">
        <!-- Header rouge avec logo et titre -->
    <div class="header">
        <div class="logo-container">
            {% if company and company.logo %}
                <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
            {% else %}
                Logo
            {% endif %}
        </div>
        <div class="document-title">
            Bon de livraison
            {% if delivery_note and delivery_note.status == 'approved' %}
                <span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-left: 10px;">✓ APPROUVÉ</span>
            {% elif delivery_note and delivery_note.status == 'draft' %}
                <span style="background: #ffc107; color: black; padding: 4px 8px; border-radius: 4px; font-size: 12px; margin-left: 10px;">📝 BROUILLON</span>
            {% endif %}
        </div>
    </div>

    <!-- Section Client avec fond gris -->
    <div class="client-section">
        <div class="client-title">Client :</div>
        <div class="client-details">
            <div class="client-info">
                <div class="info-line">
                    <span class="info-label">Nom du client :</span>
                    {{ delivery_note.client.name if delivery_note and delivery_note.client else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Ice :</span>
                    {{ delivery_note.client.ice if delivery_note and delivery_note.client and delivery_note.client.ice else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone :</span>
                    {{ delivery_note.client.phone if delivery_note and delivery_note.client else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Email :</span>
                    {{ delivery_note.client.email if delivery_note and delivery_note.client else '' }}
                </div>
            </div>
            <div class="document-info">
                <div class="info-line">
                    <span class="info-label">Date du BL :</span>
                    {{ delivery_note.date.strftime('%d.%m.%Y') if delivery_note and delivery_note.date else '1.6.2021' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Référence du BL :</span>
                    {{ delivery_note.delivery_number if delivery_note else '143' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Date de validité :</span>
                    {{ delivery_note.delivery_date.strftime('%d.%m.%Y') if delivery_note and delivery_note.delivery_date else '15.6.2021' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Objet -->
    <div class="object-section">
        <div class="object-title">Objet :</div>
        <div class="object-content">{{ delivery_note.notes if delivery_note and delivery_note.notes else '' }}</div>
    </div>

    <!-- Tableau des articles - Version simplifiée pour bon de livraison -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 60%;">Description</th>
                <th style="width: 20%;">Unité</th>
                <th style="width: 20%;">Quantité</th>
            </tr>
        </thead>
        <tbody>
            {% if delivery_note and delivery_note.items %}
                {% for item in delivery_note.items %}
                <tr>
                    <td class="description-col">{{ item.product.name if item.product else (item.description or '') }}</td>
                    <td>Unité</td>
                    <td>{{ item.quantity }}</td>
                </tr>
                {% endfor %}
                <!-- Lignes vides pour remplir l'espace -->
                {% set items_count = delivery_note.items|list|length if delivery_note and delivery_note.items else 0 %}
                {% for i in range(12 - items_count) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Lignes vides par défaut -->
                {% for i in range(12) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- Section des signatures -->
    <div class="signatures-section">
        <div class="signature-box">
            <div>Signature et cachet de client</div>
        </div>
        <div class="signature-box">
            <div>Signature et cachet de l'entreprise</div>
        </div>
    </div>

        <!-- Footer -->
        <div class="footer">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% elif company %}
                {% if company.name %}{{ company.name }}{% endif %}
                {% if company.address %} - {{ company.address }}{% endif %}
                {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
                {% if company.email %} - Email: {{ company.email }}{% endif %}
                {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
            {% endif %}
        </div>
    </div>
</body>
</html>
