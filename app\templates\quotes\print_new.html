<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number if quote else 'DEMO-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        /* Header with red background - exactly like the model */
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px 30px;
            text-align: center;
            position: relative;
            margin-bottom: 0;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-container {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            border: 2px dashed white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8pt;
            overflow: hidden;
        }

        .logo-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .document-title {
            font-size: 48pt;
            font-weight: bold;
            margin: 0;
            letter-spacing: 2px;
        }

        /* Content area */
        .content {
            padding: 20px 30px;
            background: white;
        }

        /* Client section - exactly like the model */
        .client-section {
            background: #f8f9fa;
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 0;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .client-info {
            flex: 1;
            padding-right: 30px;
        }

        .quote-info {
            flex: 1;
            text-align: left;
            padding-left: 30px;
        }

        .info-line {
            margin-bottom: 6px;
            color: #333;
            font-size: 11pt;
            line-height: 1.3;
        }

        .info-label {
            color: #333;
            font-weight: normal;
            display: inline-block;
            min-width: 120px;
        }

        /* Object section */
        .object-section {
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .object-line {
            border-bottom: 1px solid #dc3545;
            padding-bottom: 5px;
            height: 20px;
        }

        /* Items table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
        }

        .items-table th {
            background: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
        }

        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        /* Totals section */
        .totals {
            float: right;
            width: 250px;
            margin-top: 30px;
            margin-bottom: 50px;
        }

        .total-line {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 11pt;
        }

        .total-ht {
            border-bottom: 1px solid #ddd;
        }

        .total-tva {
            border-bottom: 1px solid #ddd;
        }

        .total-final {
            font-weight: bold;
            font-size: 12pt;
            color: #dc3545;
            border-bottom: 2px solid #dc3545;
            margin-top: 5px;
            padding-top: 8px;
        }

        /* Service info */
        .service-info {
            margin-top: 100px;
            font-size: 10pt;
            color: #666;
            font-style: italic;
        }

        /* Footer - using company information */
        .footer {
            position: fixed;
            bottom: 10px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #dc3545;
            font-weight: bold;
            padding: 10px 30px;
            border-top: 1px solid #dc3545;
            background: white;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .footer {
                position: static;
                margin-top: 50px;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="window.close()">Fermer</button>
    </div>

    <!-- En-tête rouge -->
    <div class="header">
        <div class="logo-container">
            {% if company and company.logo %}
                <img src="{{ url_for('static', filename='uploads/logos/' + company.logo) }}" alt="Logo">
            {% else %}
                Logo
            {% endif %}
        </div>
        <div class="document-title">Devis</div>
    </div>

    <!-- Contenu principal -->
    <div class="content">
        <!-- Section Client -->
        <div class="client-section">
            <div class="client-title">Client :</div>
            <div class="client-details">
                <div class="client-info">
                    <div class="info-line">
                        <span class="info-label">Nom du client :</span>
                        {{ quote.client.name if quote and quote.client else 'Nom du client' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Ice :</span>
                        {{ quote.client.ice if quote and quote.client and quote.client.ice else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Téléphone :</span>
                        {{ quote.client.phone if quote and quote.client else '' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Email :</span>
                        {{ quote.client.email if quote and quote.client else '' }}
                    </div>
                </div>
                <div class="quote-info">
                    <div class="info-line">
                        <span class="info-label">Date du devis :</span>
                        {{ quote.date.strftime('%d.%m.%Y') if quote and quote.date else '1.6.2021' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Référence du devis :</span>
                        {{ quote.quote_number if quote else '143' }}
                    </div>
                    <div class="info-line">
                        <span class="info-label">Date de validité :</span>
                        {{ quote.valid_until.strftime('%d.%m.%Y') if quote and quote.valid_until else '15.6.2021' }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Objet -->
        <div class="object-section">
            <div class="object-title">Objet :</div>
            <div class="object-line"></div>
        </div>

        <!-- Tableau des articles -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Unité</th>
                    <th>Quantité</th>
                    <th>Prix U.HT</th>
                    <th>Prix T.HT</th>
                </tr>
            </thead>
            <tbody>
                {% if quote and quote.items %}
                    {% for item in quote.items %}
                    <tr>
                        <td style="text-align: left;">{{ item.product.name if item.product else item.description }}</td>
                        <td>{{ item.product.unit if item.product else 'Unité' }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ "%.2f"|format(item.unit_price) }}</td>
                        <td>{{ "%.2f"|format(item.total_price) }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td style="text-align: left;"></td>
                        <td></td>
                        <td></td>
                        <td>0,00</td>
                        <td>0,00</td>
                    </tr>
                    <tr>
                        <td style="text-align: left;"></td>
                        <td></td>
                        <td></td>
                        <td>0,00</td>
                        <td>0,00</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>

        <!-- Totaux -->
        <div class="totals">
            <div class="total-line total-ht">
                <span>Total HT</span>
                <span>{{ "%.2f"|format(quote.subtotal) if quote else '1 123,00' }}</span>
            </div>
            <div class="total-line total-tva">
                <span>Total TVA</span>
                <span>{{ "%.2f"|format(quote.tax_amount) if quote and quote.tax_amount else '224,6' }}</span>
            </div>
            <div class="total-line total-final">
                <span>Total TTC</span>
                <span>{{ "%.2f"|format(quote.total) if quote else '1 347,6' }}</span>
            </div>
        </div>

        <div style="clear: both;"></div>

        <!-- Informations de service -->
        <div class="service-info">
            <div>Service après-vente - Garantie :</div>
            <div>Date de début de la prestation : 14/05/2022</div>
        </div>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% else %}
            Pied de page ( Prendre des informations de Pied de page en dans Informations de l'Entreprise )
        {% endif %}
    </div>
</body>
</html>
