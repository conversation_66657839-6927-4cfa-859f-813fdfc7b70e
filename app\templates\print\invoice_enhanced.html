<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/print_professional.css') }}">
    <style>
        /* تخصيصات إضافية للفواتير */
        .invoice-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .invoice-title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .invoice-subtitle {
            text-align: center;
            font-size: 12pt;
            opacity: 0.9;
        }
        
        .payment-status {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 10pt;
            text-align: center;
            margin: 10px 0;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-overdue {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .payment-terms {
            background: #e9ecef;
            padding: 10px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            font-size: 10pt;
        }
        
        .due-date-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 15px 0;
            border-radius: 5px;
            font-size: 10pt;
            text-align: center;
            font-weight: bold;
        }
        
        .bank-details {
            background: #f8f9fa;
            padding: 15px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .bank-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #28a745;
        }
        
        .bank-info {
            font-size: 9pt;
            line-height: 1.4;
        }
    </style>
</head>
<body class="invoice-document">
    <!-- أزرار التحكم -->
    <div class="control-buttons no-print">
        <button class="btn btn-print" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-close" onclick="window.close()">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="document-container">
        <!-- ترويسة الفاتورة -->
        <div class="invoice-header">
            <div class="invoice-title">فاتورة</div>
            <div class="invoice-subtitle">FACTURE / INVOICE</div>
        </div>

        <!-- حالة الدفع -->
        <div class="payment-status 
            {% if invoice.status == 'paid' %}status-paid
            {% elif invoice.status == 'pending' %}status-pending
            {% elif invoice.status == 'overdue' %}status-overdue
            {% endif %}">
            {% if invoice.status == 'paid' %}✓ مدفوعة
            {% elif invoice.status == 'pending' %}⏳ في انتظار الدفع
            {% elif invoice.status == 'overdue' %}⚠️ متأخرة الدفع
            {% else %}{{ invoice.status }}{% endif %}
        </div>

        <!-- معلومات الشركة والعميل -->
        <div class="main-info">
            <div class="info-section">
                <div class="section-title">معلومات الشركة</div>
                {% if company %}
                <div class="company-name">{{ company.name }}</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ company.address }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ company.phone }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">البريد:</span>
                    <span class="info-value">{{ company.email }}</span>
                </div>
                {% if company.ice %}
                <div class="info-item">
                    <span class="info-label">ICE:</span>
                    <span class="info-value">{{ company.ice }}</span>
                </div>
                {% endif %}
                {% if company.rc %}
                <div class="info-item">
                    <span class="info-label">RC:</span>
                    <span class="info-value">{{ company.rc }}</span>
                </div>
                {% endif %}
                {% else %}
                <div class="company-name">شركة إدارة طفايات الحريق</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">الرباط، المغرب</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">+212 5 37 XX XX XX</span>
                </div>
                {% endif %}
            </div>

            <div class="info-section">
                <div class="section-title">فاتورة إلى</div>
                {% if invoice.client %}
                <div class="client-name">{{ invoice.client.name }}</div>
                <div class="info-item">
                    <span class="info-label">العنوان:</span>
                    <span class="info-value">{{ invoice.client.address or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المدينة:</span>
                    <span class="info-value">{{ invoice.client.city or '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الهاتف:</span>
                    <span class="info-value">{{ invoice.client.phone or '-' }}</span>
                </div>
                {% if invoice.client.ice %}
                <div class="info-item">
                    <span class="info-label">ICE:</span>
                    <span class="info-value">{{ invoice.client.ice }}</span>
                </div>
                {% endif %}
                {% else %}
                <div class="client-name">عميل غير محدد</div>
                {% endif %}
            </div>

            <div class="info-section">
                <div class="section-title">تفاصيل الفاتورة</div>
                <div class="info-item">
                    <span class="info-label">رقم الفاتورة:</span>
                    <span class="info-value">{{ invoice.invoice_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الإصدار:</span>
                    <span class="info-value">{{ invoice.date.strftime('%d/%m/%Y') if invoice.date else '-' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الاستحقاق:</span>
                    <span class="info-value">{{ invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else '-' }}</span>
                </div>
                {% if invoice.quote %}
                <div class="info-item">
                    <span class="info-label">عرض السعر:</span>
                    <span class="info-value">{{ invoice.quote.quote_number }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- إشعار تاريخ الاستحقاق -->
        {% if invoice.due_date %}
        <div class="due-date-notice">
            <strong>تنبيه:</strong> تاريخ استحقاق الدفع: {{ invoice.due_date.strftime('%d/%m/%Y') }}
        </div>
        {% endif %}

        <!-- جدول المنتجات -->
        <div class="table-container">
            <table class="professional-table">
                <thead>
                    <tr>
                        <th class="col-number">#</th>
                        <th class="col-description">الوصف</th>
                        <th class="col-unit">الوحدة</th>
                        <th class="col-quantity">الكمية</th>
                        <th class="col-price">السعر الوحدة (درهم)</th>
                        <th class="col-total">المجموع (درهم)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td class="col-number">{{ loop.index }}</td>
                        <td class="col-description">
                            <strong>{{ item.product.name if item.product else 'منتج محذوف' }}</strong>
                            {% if item.product and item.product.reference %}
                            <br><small>المرجع: {{ item.product.reference }}</small>
                            {% endif %}
                            {% if item.product and item.product.description %}
                            <br><small>{{ item.product.description }}</small>
                            {% endif %}
                        </td>
                        <td class="col-unit">قطعة</td>
                        <td class="col-quantity">{{ item.quantity }}</td>
                        <td class="col-price">{{ "%.2f"|format(item.unit_price) }}</td>
                        <td class="col-total">{{ "%.2f"|format(item.total) }}</td>
                    </tr>
                    {% endfor %}
                    
                    <!-- صفوف فارغة للمظهر -->
                    {% for i in range(5 - invoice.items|length) %}
                    {% if i >= 0 %}
                    <tr class="empty-row">
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- قسم المجاميع -->
        <div class="totals-section">
            <div class="totals-container">
                <table class="totals-table">
                    <tr>
                        <td class="total-label">المجموع الفرعي:</td>
                        <td class="total-value">{{ "%.2f"|format(invoice.subtotal) }} درهم</td>
                    </tr>
                    {% if invoice.tax_rate and invoice.tax_rate > 0 %}
                    <tr>
                        <td class="total-label">الضريبة ({{ invoice.tax_rate }}%):</td>
                        <td class="total-value">{{ "%.2f"|format(invoice.tax_amount) }} درهم</td>
                    </tr>
                    {% endif %}
                    {% if invoice.discount and invoice.discount > 0 %}
                    <tr>
                        <td class="total-label">الخصم:</td>
                        <td class="total-value">-{{ "%.2f"|format(invoice.discount) }} درهم</td>
                    </tr>
                    {% endif %}
                    <tr class="final-total">
                        <td class="total-label">المبلغ المستحق:</td>
                        <td class="total-value">{{ "%.2f"|format(invoice.total) }} درهم</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- شروط الدفع -->
        <div class="payment-terms">
            <strong>شروط الدفع:</strong>
            الدفع خلال 30 يوماً من تاريخ الفاتورة. في حالة التأخير، سيتم تطبيق فوائد تأخير بنسبة 1.5% شهرياً.
        </div>

        <!-- تفاصيل البنك -->
        <div class="bank-details">
            <div class="bank-title">تفاصيل الحساب البنكي للتحويل:</div>
            <div class="bank-info">
                {% if company and company.bank_details %}
                {{ company.bank_details|nl2br }}
                {% else %}
                <strong>البنك الشعبي - فرع الرباط</strong><br>
                رقم الحساب: 181 810 21211 9001238 000 1 31<br>
                RIB: 011 810 0021211900123800013 17<br>
                SWIFT: BMCEMAMC
                {% endif %}
            </div>
        </div>

        <!-- قسم التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-title">استلام العميل</div>
                <div class="signature-line"></div>
                <div class="signature-date">التاريخ: ___________</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">توقيع الشركة</div>
                <div class="signature-line"></div>
                <div class="signature-date">التاريخ: ___________</div>
            </div>
        </div>

        <!-- تذييل الوثيقة -->
        <div class="document-footer">
            <div class="footer-content">
                {% if company and company.footer_text %}
                <p>{{ company.footer_text|nl2br }}</p>
                {% else %}
                <p>شكراً لثقتكم بنا - نحن في خدمتكم دائماً</p>
                <p>للاستفسارات: <EMAIL> | الهاتف: +212 5 37 XX XX XX</p>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
