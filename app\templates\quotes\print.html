<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Devis N° {{ quote.quote_number if quote else 'DEMO-001' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 15mm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        /* Header with red background */
        .header {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            margin: -15mm -15mm 20px -15mm;
            text-align: center;
            position: relative;
        }

        .logo-placeholder {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            border: 2px dashed white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
        }

        .document-title {
            font-size: 36pt;
            font-weight: bold;
            margin: 0;
        }

        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .info-box {
            width: 48%;
        }

        .info-title {
            font-weight: bold;
            color: #dc3545;
            border-bottom: 1px solid #dc3545;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .info-line {
            margin-bottom: 5px;
        }

        .table-container {
            margin: 30px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            background-color: #dc3545;
            color: white;
            font-weight: bold;
        }

        .text-right {
            text-align: right;
        }

        .totals {
            margin-top: 20px;
            float: right;
            width: 300px;
        }

        .total-line {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }

        .total-final {
            font-weight: bold;
            font-size: 14pt;
            border-top: 2px solid #dc3545;
            border-bottom: 2px solid #dc3545;
            margin-top: 10px;
            padding: 10px 0;
        }

        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10pt;
            color: #666;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            body {
                background: white !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="window.close()">Fermer</button>
    </div>

    <!-- En-tête -->
    <div class="header">
        <div class="company-name">
            {% if company and company.name %}
                {{ company.name }}
            {% else %}
                GESTION D'EXTINCTEURS
            {% endif %}
        </div>
        <div style="font-size: 12pt; color: #666;">
            {% if company and company.address %}
                {{ company.address }}<br>
            {% endif %}
            {% if company and company.phone %}
                Tél: {{ company.phone }}
            {% endif %}
            {% if company and company.email %}
                | Email: {{ company.email }}
            {% endif %}
        </div>
        <div class="document-title">DEVIS</div>
    </div>

    <!-- Informations -->
    <div class="info-section">
        <div class="info-box">
            <div class="info-title">DEVIS POUR:</div>
            {% if quote and quote.client %}
                <div class="info-line"><strong>{{ quote.client.name }}</strong></div>
                {% if quote.client.address %}
                    <div class="info-line">{{ quote.client.address }}</div>
                {% endif %}
                {% if quote.client.city %}
                    <div class="info-line">{{ quote.client.city }}</div>
                {% endif %}
                {% if quote.client.phone %}
                    <div class="info-line">Tél: {{ quote.client.phone }}</div>
                {% endif %}
                {% if quote.client.email %}
                    <div class="info-line">Email: {{ quote.client.email }}</div>
                {% endif %}
            {% else %}
                <div class="info-line"><strong>Client Demo</strong></div>
                <div class="info-line">123 Rue Example</div>
                <div class="info-line">Casablanca, Maroc</div>
                <div class="info-line">Tél: +212 5 22 XX XX XX</div>
            {% endif %}
        </div>

        <div class="info-box">
            <div class="info-title">DÉTAILS DU DEVIS:</div>
            <div class="info-line">
                <strong>N° Devis:</strong>
                {{ quote.quote_number if quote else 'DEMO-001' }}
            </div>
            <div class="info-line">
                <strong>Date:</strong>
                {{ quote.date.strftime('%d/%m/%Y') if quote and quote.date else '01/01/2024' }}
            </div>
            <div class="info-line">
                <strong>Validité:</strong>
                {{ quote.valid_until.strftime('%d/%m/%Y') if quote and quote.valid_until else '31/01/2024' }}
            </div>
            <div class="info-line">
                <strong>Statut:</strong>
                {% if quote %}
                    {% if quote.status == 'draft' %}Brouillon
                    {% elif quote.status == 'sent' %}Envoyé
                    {% elif quote.status == 'accepted' %}Accepté
                    {% elif quote.status == 'rejected' %}Refusé
                    {% else %}{{ quote.status }}{% endif %}
                {% else %}
                    Brouillon
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Tableau des produits -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 45%;">Désignation</th>
                    <th style="width: 10%;">Qté</th>
                    <th style="width: 15%;">Prix Unit. (DH)</th>
                    <th style="width: 15%;">Total (DH)</th>
                </tr>
            </thead>
            <tbody>
                {% if quote and quote.items %}
                    {% for item in quote.items %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td>
                            <strong>{{ item.product.name if item.product else 'Produit supprimé' }}</strong>
                            {% if item.product and item.product.reference %}
                                <br><small>Réf: {{ item.product.reference }}</small>
                            {% endif %}
                        </td>
                        <td class="text-right">{{ item.quantity }}</td>
                        <td class="text-right">{{ "%.2f"|format(item.unit_price) }}</td>
                        <td class="text-right">{{ "%.2f"|format(item.total) }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <!-- Données de démonstration -->
                    <tr>
                        <td>1</td>
                        <td><strong>Extincteur CO2 5kg</strong><br><small>Réf: EXT-CO2-5</small></td>
                        <td class="text-right">2</td>
                        <td class="text-right">450.00</td>
                        <td class="text-right">900.00</td>
                    </tr>
                    <tr>
                        <td>2</td>
                        <td><strong>Extincteur Poudre 6kg</strong><br><small>Réf: EXT-POU-6</small></td>
                        <td class="text-right">3</td>
                        <td class="text-right">320.00</td>
                        <td class="text-right">960.00</td>
                    </tr>
                    <tr>
                        <td>3</td>
                        <td><strong>Vérification annuelle</strong><br><small>Service de maintenance</small></td>
                        <td class="text-right">5</td>
                        <td class="text-right">80.00</td>
                        <td class="text-right">400.00</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>

    <!-- Totaux -->
    <div class="totals">
        <div class="total-line">
            <span>Sous-total:</span>
            <span>{{ "%.2f"|format(quote.subtotal) if quote else '2,260.00' }} DH</span>
        </div>
        {% if quote and quote.tax_rate and quote.tax_rate > 0 %}
        <div class="total-line">
            <span>TVA ({{ quote.tax_rate }}%):</span>
            <span>{{ "%.2f"|format(quote.tax_amount) }} DH</span>
        </div>
        {% endif %}
        {% if quote and quote.discount and quote.discount > 0 %}
        <div class="total-line">
            <span>Remise:</span>
            <span>-{{ "%.2f"|format(quote.discount) }} DH</span>
        </div>
        {% endif %}
        <div class="total-line total-final">
            <span>TOTAL:</span>
            <span>{{ "%.2f"|format(quote.total) if quote else '2,260.00' }} DH</span>
        </div>
    </div>

    <div style="clear: both;"></div>

    <!-- Conditions -->
    <div style="margin-top: 40px;">
        <h4 style="color: #dc3545;">Conditions:</h4>
        <ul style="font-size: 10pt;">
            <li>Devis valable 30 jours</li>
            <li>Livraison sous 7-10 jours ouvrables</li>
            <li>Paiement: 50% à la commande, solde à la livraison</li>
            <li>Garantie 1 an sur tous les produits</li>
        </ul>
    </div>

    <!-- Pied de page -->
    <div class="footer">
        <p>Merci de votre confiance</p>
        <p style="font-size: 9pt;">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% else %}
                Pour toute question: <EMAIL> | Tél: +212 5 37 XX XX XX
            {% endif %}
        </p>
    </div>
</body>
</html>
