{% extends 'base.html' %}

{% block title %}Nouveau Bon de Livraison - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h1 class="display-6">
            <i class="fas fa-truck me-2"></i>Nouveau Bon de Livraison
        </h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('main.commercial_delivery_notes') }}">Bons de Livraison</a></li>
                <li class="breadcrumb-item active">Nouveau</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus-circle me-2"></i>Informations du Bon de Livraison
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('main.commercial_delivery_notes_create') }}">
                    {{ form.hidden_tag() }}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.client_id.label(class="form-label required") }}
                                {{ form.client_id(class="form-select", required=true) }}
                                {% if form.client_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.client_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.invoice_id.label(class="form-label") }}
                                {{ form.invoice_id(class="form-select") }}
                                <small class="form-text text-muted">Optionnel - Basé sur une facture existante</small>
                                {% if form.invoice_id.errors %}
                                    <div class="text-danger">
                                        {% for error in form.invoice_id.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.date.label(class="form-label") }}
                                {{ form.date(class="form-control", type="date") }}
                                {% if form.date.errors %}
                                    <div class="text-danger">
                                        {% for error in form.date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select", required=true) }}
                                {% if form.status.errors %}
                                    <div class="text-danger">
                                        {% for error in form.status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3", placeholder="Notes ou instructions spéciales...") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.commercial_delivery_notes') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Retour
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informations
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>Conseils</h6>
                    <ul class="mb-0">
                        <li>Sélectionnez le client destinataire</li>
                        <li>Vous pouvez baser le bon sur une facture existante</li>
                        <li>Le numéro sera généré automatiquement</li>
                        <li>Vous pourrez ajouter des produits après création</li>
                    </ul>
                </div>

                <div class="mt-3">
                    <h6><i class="fas fa-chart-bar me-2"></i>Statistiques</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <div class="h5 mb-0 text-primary">{{ delivery_notes_count or 0 }}</div>
                                <small class="text-muted">Bons créés</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <div class="h5 mb-0 text-success">{{ delivered_count or 0 }}</div>
                                <small class="text-muted">Livrés</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-truck me-2"></i>Statuts Disponibles
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <span class="badge bg-secondary me-2">Brouillon</span>
                    <small>En préparation</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-success me-2">Livré</span>
                    <small>Livraison effectuée</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-warning me-2">Retourné</span>
                    <small>Produits retournés</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-set today's date if not set
    const dateField = document.querySelector('input[name="date"]');
    if (dateField && !dateField.value) {
        const today = new Date().toISOString().split('T')[0];
        dateField.value = today;
    }

    // Client selection change handler
    const clientSelect = document.querySelector('select[name="client_id"]');
    if (clientSelect) {
        clientSelect.addEventListener('change', function() {
            if (this.value) {
                // You can add AJAX call here to load client-specific data
                console.log('Client selected:', this.value);
            }
        });
    }

    // Invoice selection change handler
    const invoiceSelect = document.querySelector('select[name="invoice_id"]');
    if (invoiceSelect) {
        invoiceSelect.addEventListener('change', function() {
            if (this.value && this.value !== '0') {
                // Show info that items will be copied from invoice
                const alert = document.createElement('div');
                alert.className = 'alert alert-info mt-2';
                alert.innerHTML = '<i class="fas fa-info-circle me-2"></i>Les produits de cette facture seront automatiquement ajoutés au bon de livraison.';

                // Remove existing alerts
                const existingAlert = invoiceSelect.parentNode.querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                invoiceSelect.parentNode.appendChild(alert);
            } else {
                // Remove alert if no invoice selected
                const existingAlert = invoiceSelect.parentNode.querySelector('.alert');
                if (existingAlert) {
                    existingAlert.remove();
                }
            }
        });
    }
});
</script>
{% endblock %}
