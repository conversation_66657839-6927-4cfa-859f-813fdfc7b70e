{% extends 'base.html' %}

{% block title %}Modifier <PERSON> {{ delivery_note.delivery_note_number }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<style>
    .document-container {
        width: 210mm;
        min-height: 297mm;
        margin: 0 auto;
        background-color: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .content-wrapper {
        flex: 1;
    }

    /* Header rouge avec logo et titre */
    .header {
        background-color: #dc3545;
        color: white;
        height: 80px;
        display: flex;
        align-items: center;
        padding: 0 30px;
        margin-bottom: 0;
    }

    .logo-container {
        width: 200px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10pt;
        margin-right: 30px;
        overflow: hidden;
    }

    .logo-container img {
        max-width: 200px;
        max-height: 80px;
        object-fit: contain;
    }

    .document-title {
        font-size: 36pt;
        font-weight: bold;
        flex: 1;
        text-align: center;
        margin: 0;
    }

    /* Section Client avec fond gris */
    .client-section {
        background-color: #f8f9fa;
        padding: 15px 30px;
        margin-bottom: 0;
    }

    .client-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
        font-size: 12pt;
    }

    .client-details {
        display: flex;
        justify-content: space-between;
    }

    .client-info, .document-info {
        flex: 1;
    }

    .document-info {
        margin-left: 50px;
    }

    .info-line {
        margin-bottom: 5px;
        font-size: 11pt;
        display: flex;
        align-items: center;
    }

    .info-label {
        color: #666;
        display: inline-block;
        min-width: 120px;
        font-weight: bold;
    }

    .editable-field {
        border: 1px solid #ddd;
        padding: 5px;
        border-radius: 3px;
        background: white;
        flex: 1;
        margin-left: 10px;
    }

    .editable-select {
        border: 1px solid #ddd;
        padding: 5px;
        border-radius: 3px;
        background: white;
        flex: 1;
        margin-left: 10px;
    }

    /* Section Objet */
    .object-section {
        padding: 15px 30px;
        margin-bottom: 20px;
    }

    .object-title {
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
        font-size: 12pt;
    }

    .object-input {
        border-bottom: 1px solid #dc3545;
        border-top: none;
        border-left: none;
        border-right: none;
        width: 100%;
        padding: 5px 0;
        background: transparent;
        outline: none;
    }

    /* Tableau des articles */
    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0 30px;
        width: calc(100% - 60px);
    }

    .items-table th {
        background-color: #dc3545;
        color: white;
        padding: 12px 8px;
        text-align: center;
        font-weight: bold;
        border: 1px solid #dc3545;
        font-size: 11pt;
    }

    .items-table td {
        padding: 8px;
        text-align: center;
        border: 1px solid #ddd;
        font-size: 11pt;
        height: 35px;
    }

    .items-table tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .items-table tr:nth-child(odd) {
        background-color: white;
    }

    .description-col {
        text-align: left !important;
        padding-left: 15px !important;
    }

    .table-input {
        border: none;
        background: transparent;
        width: 100%;
        text-align: center;
        outline: none;
    }

    .table-input.description {
        text-align: left;
    }

    /* Section des signatures */
    .signatures-section {
        margin: 50px 30px 30px 30px;
        display: flex;
        justify-content: space-between;
    }

    .signature-box {
        width: 45%;
        height: 80px;
        border: 1px solid #ddd;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        padding: 10px;
        font-size: 10pt;
        color: #666;
    }

    /* Footer */
    .footer {
        margin-top: auto;
        padding: 20px 30px;
        text-align: center;
        font-size: 10pt;
        color: #666;
        border-top: 1px solid #ddd;
        background-color: #f8f9fa;
        position: sticky;
        bottom: 0;
    }

    /* Boutons d'action */
    .action-buttons {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }

    .btn {
        margin: 3px;
        padding: 8px 15px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-weight: bold;
    }

    .btn-save {
        background: #28a745;
        color: white;
    }

    .btn-print {
        background: #dc3545;
        color: white;
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
    }

    .add-row-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 3px;
        cursor: pointer;
        margin: 10px 30px;
    }

    .delete-row-btn {
        background: #dc3545;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 30px;
        height: 30px;
    }

    .delete-row-btn:hover {
        background: #c82333;
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
    }

    .delete-row-btn:active {
        transform: scale(0.95);
    }

    .delete-row-btn i {
        font-size: 12px;
    }

    /* إخفاء عناصر عند الطباعة */
    @media print {
        .action-buttons {
            display: none !important;
        }

        .items-table th:last-child,
        .items-table td:last-child {
            display: none !important;
        }

        .add-row-btn {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .footer {
            position: static;
            margin-top: 50px;
        }
    }
</style>

<div class="action-buttons">
    <button class="btn btn-save" onclick="updateDeliveryNote()">
        <i class="fas fa-save"></i> Modifier
    </button>
    <a href="{{ url_for('delivery_notes.index') }}" class="btn btn-cancel">
        <i class="fas fa-times"></i> Annuler
    </a>
</div>

<div class="document-container">
    <div class="content-wrapper">
        <!-- Header rouge avec logo et titre -->
        <div class="header">
            <div class="logo-container">
                {% if company and company.logo %}
                    <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
                {% else %}
                    Logo
                {% endif %}
            </div>
            <div class="document-title">Modifier Bon de livraison</div>
        </div>

    <!-- Section Client avec fond gris -->
    <div class="client-section">
        <div class="client-title">Client :</div>
        <div class="client-details">
            <div class="client-info">
                <div class="info-line">
                    <span class="info-label">Nom du client :</span>
                    <select class="editable-select" id="client_id" name="client_id" onchange="updateClientInfo()">
                        <option value="">Sélectionner un client</option>
                        {% for client in clients %}
                            <option value="{{ client.id }}"
                                    data-name="{{ client.name }}"
                                    data-address="{{ client.address }}"
                                    data-phone="{{ client.phone }}"
                                    data-email="{{ client.email }}"
                                    data-ice="{{ client.ice }}"
                                    {% if delivery_note.client_id == client.id %}selected{% endif %}>
                                {{ client.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="info-line">
                    <span class="info-label">Ice :</span>
                    <input type="text" class="editable-field" id="client_ice" readonly value="{{ delivery_note.client.ice if delivery_note.client else '' }}">
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone :</span>
                    <input type="text" class="editable-field" id="client_phone" readonly value="{{ delivery_note.client.phone if delivery_note.client else '' }}">
                </div>
                <div class="info-line">
                    <span class="info-label">Email :</span>
                    <input type="text" class="editable-field" id="client_email" readonly value="{{ delivery_note.client.email if delivery_note.client else '' }}">
                </div>
            </div>
            <div class="document-info">
                <div class="info-line">
                    <span class="info-label">Date du BL :</span>
                    <input type="date" class="editable-field" id="delivery_date" value="{{ delivery_note.date.strftime('%Y-%m-%d') if delivery_note.date else '' }}">
                </div>
                <div class="info-line">
                    <span class="info-label">Référence du BL :</span>
                    <input type="text" class="editable-field" id="delivery_number" value="{{ delivery_note.delivery_note_number }}" readonly>
                </div>
                <div class="info-line">
                    <span class="info-label">Date de livraison :</span>
                    <input type="date" class="editable-field" id="delivery_date_actual" value="{{ delivery_note.delivery_date.strftime('%Y-%m-%d') if delivery_note.delivery_date else '' }}">
                </div>
            </div>
        </div>
    </div>

    <!-- Section Objet -->
    <div class="object-section">
        <div class="object-title">Objet :</div>
        <input type="text" class="object-input" id="delivery_object" placeholder="Objet du bon de livraison..." value="{{ delivery_note.notes or '' }}">
    </div>

    <!-- Tableau des articles - Version simplifiée pour bon de livraison -->
    <table class="items-table" id="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 17%;">Unité</th>
                <th style="width: 17%;">Quantité</th>
                <th style="width: 16%;">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% if delivery_note.items %}
                {% for item in delivery_note.items %}
                <tr>
                    <td class="description-col">
                        <input type="text" class="table-input description" value="{{ item.description or (item.product.name if item.product else '') }}">
                    </td>
                    <td>
                        <input type="text" class="table-input" value="{{ item.product.unit if item.product else 'Unité' }}">
                    </td>
                    <td>
                        <input type="number" class="table-input" value="{{ item.quantity }}">
                    </td>
                    <td>
                        <button class="delete-row-btn" onclick="deleteTableRow(this)" title="Supprimer cette ligne">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <button class="add-row-btn" onclick="addTableRow()">
        <i class="fas fa-plus"></i> Ajouter une ligne
    </button>

    <!-- Section des signatures -->
    <div class="signatures-section">
        <div class="signature-box">
            <div>Signature et cachet de client</div>
        </div>
        <div class="signature-box">
            <div>Signature et cachet de l'entreprise</div>
        </div>
    </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        {% if company and company.footer_text %}
            {{ company.footer_text }}
        {% elif company %}
            {% if company.name %}{{ company.name }}{% endif %}
            {% if company.address %} - {{ company.address }}{% endif %}
            {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
            {% if company.email %} - Email: {{ company.email }}{% endif %}
            {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
        {% endif %}
    </div>
</div>

<script>
// Mise à jour des informations client lors de la sélection
function updateClientInfo() {
    const select = document.getElementById('client_id');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        document.getElementById('client_ice').value = selectedOption.dataset.ice || '';
        document.getElementById('client_phone').value = selectedOption.dataset.phone || '';
        document.getElementById('client_email').value = selectedOption.dataset.email || '';
    } else {
        document.getElementById('client_ice').value = '';
        document.getElementById('client_phone').value = '';
        document.getElementById('client_email').value = '';
    }
}

// إضافة صف جديد
function addTableRow() {
    const tbody = document.querySelector('#items-table tbody');
    const newRow = tbody.insertRow();

    newRow.innerHTML = `
        <td class="description-col">
            <input type="text" class="table-input description" placeholder="Description du produit...">
        </td>
        <td>
            <input type="text" class="table-input" placeholder="Unité">
        </td>
        <td>
            <input type="number" class="table-input" placeholder="0">
        </td>
        <td>
            <button class="delete-row-btn" onclick="deleteTableRow(this)" title="Supprimer cette ligne">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
}

// حذف صف
function deleteTableRow(button) {
    const row = button.closest('tr');
    row.remove();
}

// تحديث بون الليفريزون
function updateDeliveryNote() {
    // جمع البيانات من النموذج
    const deliveryData = {
        client_id: document.getElementById('client_id').value,
        date: document.getElementById('delivery_date').value,
        delivery_date: document.getElementById('delivery_date_actual').value,
        object: document.getElementById('delivery_object').value,
        items: []
    };

    // جمع عناصر الجدول
    const rows = document.querySelectorAll('#items-table tbody tr');
    rows.forEach(row => {
        const description = row.cells[0].querySelector('input').value;
        const unit = row.cells[1].querySelector('input').value;
        const quantity = row.cells[2].querySelector('input').value;

        if (description && quantity) {
            deliveryData.items.push({
                description: description,
                unit: unit,
                quantity: parseFloat(quantity)
            });
        }
    });

    // إرسال البيانات للخادم
    fetch('{{ url_for("delivery_notes.edit_interactive", id=delivery_note.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(deliveryData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Bon de livraison modifié avec succès!');
            window.location.href = '{{ url_for("delivery_notes.index") }}';
        } else {
            alert('Erreur lors de la modification du bon de livraison: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Erreur de connexion au serveur');
    });
}


</script>
{% endblock %}
