{% extends 'base.html' %}

{% block title %}Sort<PERSON> - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6">
                        <i class="fas fa-arrow-circle-up me-2 text-danger"></i>Sortie <PERSON>
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('main.products') }}">Produits & Stock</a></li>
                            <li class="breadcrumb-item active">Sort<PERSON></li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ url_for('main.products') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-arrow-circle-up me-2"></i>Enregistrer une Sortie de Stock
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Sortie de stock :</strong> Utilisez cette fonction pour enregistrer la sortie de produits 
                                (ventes, livraisons, consommation interne, etc.)
                            </div>

                            <form method="POST">
                                {{ form.hidden_tag() }}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_id" class="form-label required">
                                                <i class="fas fa-box me-1"></i>Produit
                                            </label>
                                            {{ form.product_id(class="form-select") }}
                                            {% if form.product_id.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.product_id.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                            <div class="form-text">Le stock disponible sera affiché après sélection</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="quantity" class="form-label required">
                                                <i class="fas fa-minus-circle me-1"></i>Quantité à sortir
                                            </label>
                                            {{ form.quantity(class="form-control", min="1", placeholder="Ex: 3") }}
                                            {% if form.quantity.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.quantity.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                            <div class="form-text">Vérifiez que la quantité ne dépasse pas le stock disponible</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="reference_document" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i>Référence Document (optionnel)
                                    </label>
                                    {{ form.reference_document(class="form-control", placeholder="Ex: BON-SORT-2024-001, Commande #456") }}
                                    {% if form.reference_document.errors %}
                                        <div class="text-danger">
                                            {% for error in form.reference_document.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Numéro de bon de sortie, commande client, etc.</div>
                                </div>

                                <div class="mb-4">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>Notes (optionnel)
                                    </label>
                                    {{ form.notes(class="form-control", rows="4", placeholder="Destination, client, raison de la sortie...") }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger">
                                            {% for error in form.notes.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Précisez la destination, le client, la raison de la sortie, etc.</div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ url_for('main.products') }}" class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    {{ form.submit(class="btn btn-danger", value="Enregistrer la Sortie") }}
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                    <h6 class="card-title">Types de Sorties</h6>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-check text-success me-1"></i> Ventes clients</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Livraisons</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Consommation interne</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Transferts</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-circle fa-2x text-danger mb-2"></i>
                                    <h6 class="card-title">Attention</h6>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-arrow-right text-danger me-1"></i> Vérifiez le stock disponible</li>
                                        <li><i class="fas fa-arrow-right text-danger me-1"></i> Documentez la destination</li>
                                        <li><i class="fas fa-arrow-right text-danger me-1"></i> Gardez les justificatifs</li>
                                        <li><i class="fas fa-arrow-right text-danger me-1"></i> Informez les responsables</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on product selection
    const productSelect = document.querySelector('select[name="product_id"]');
    if (productSelect) {
        productSelect.focus();
    }

    // Form validation
    const form = document.querySelector('form');
    const quantityInput = document.querySelector('input[name="quantity"]');
    
    form.addEventListener('submit', function(e) {
        const quantity = parseInt(quantityInput.value);
        
        if (!quantity || quantity <= 0) {
            e.preventDefault();
            alert('Veuillez saisir une quantité valide (supérieure à 0).');
            quantityInput.focus();
            return false;
        }
        
        // Confirmation
        const productSelect = document.querySelector('select[name="product_id"]');
        const productName = productSelect.options[productSelect.selectedIndex].text;
        
        if (!confirm(`Confirmer la sortie de ${quantity} unité(s) de "${productName}" du stock ?`)) {
            e.preventDefault();
            return false;
        }
    });
    
    // Show current stock when product is selected
    const productSelect = document.querySelector('select[name="product_id"]');
    if (productSelect) {
        productSelect.addEventListener('change', function() {
            if (this.value) {
                // Extract current stock from option text (if available)
                const optionText = this.options[this.selectedIndex].text;
                const stockMatch = optionText.match(/Stock:\s*(\d+)/);
                
                if (stockMatch) {
                    const currentStock = parseInt(stockMatch[1]);
                    const quantityInput = document.querySelector('input[name="quantity"]');
                    quantityInput.setAttribute('max', currentStock);
                    
                    // Show stock info
                    let stockInfo = document.querySelector('.stock-info');
                    if (!stockInfo) {
                        stockInfo = document.createElement('div');
                        stockInfo.className = 'stock-info alert alert-info mt-2';
                        quantityInput.parentNode.appendChild(stockInfo);
                    }
                    stockInfo.innerHTML = `<i class="fas fa-info-circle me-1"></i>Stock disponible: <strong>${currentStock}</strong> unités`;
                }
            }
        });
    }
});
</script>
{% endblock %}
