{% extends 'base.html' %}

{% block title %}{{ product.name }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-box me-2"></i>{{ product.name }}
    </h1>
    <div>
        <a href="{{ url_for('products.edit', id=product.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('products.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Détails du produit</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Référence:</div>
                    <div class="col-md-8">{{ product.reference }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Type:</div>
                    <div class="col-md-8">{{ product.product_type }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Prix unitaire:</div>
                    <div class="col-md-8">{{ product.unit_price }} MAD</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Quantité en stock:</div>
                    <div class="col-md-8">
                        {% if product.current_quantity < 5 %}
                        <span class="badge bg-danger">{{ product.current_quantity }}</span>
                        {% else %}
                        <span class="badge bg-success">{{ product.current_quantity }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Description:</div>
                    <div class="col-md-8">{{ product.description or 'Aucune description' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Date de création:</div>
                    <div class="col-md-8">{{ product.created_at.strftime('%d/%m/%Y %H:%M') }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Dernière mise à jour:</div>
                    <div class="col-md-8">{{ product.updated_at.strftime('%d/%m/%Y %H:%M') }}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('stock.return_stock') }}" class="btn btn-success">
                        <i class="fas fa-undo-alt me-2"></i>Retour au stock
                    </a>
                    <a href="{{ url_for('main.products') }}" class="btn btn-info">
                        <i class="fas fa-boxes me-2"></i>Gérer Stock
                    </a>
                    <button type="button" class="btn btn-danger btn-delete" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer le produit
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Historique des mouvements</h5>
    </div>
    <div class="card-body">
        {% if product.stock_movements.all() %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Quantité</th>
                        <th>Document de référence</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    {% for movement in product.stock_movements.order_by(StockMovement.date.desc()).all() %}
                    <tr>
                        <td>{{ movement.date.strftime('%d/%m/%Y %H:%M') }}</td>
                        <td>
                            {% if movement.movement_type == 'IN' %}
                            <span class="badge bg-success">Entrée</span>
                            {% else %}
                            <span class="badge bg-danger">Sortie</span>
                            {% endif %}
                        </td>
                        <td>{{ movement.quantity }}</td>
                        <td>{{ movement.reference_document or '-' }}</td>
                        <td>{{ movement.notes or '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-muted">Aucun mouvement de stock pour ce produit.</p>
        {% endif %}
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le produit <strong>{{ product.name }}</strong> ?</p>
                <p class="text-danger">Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('products.delete', id=product.id) }}" method="post">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
