{% extends 'base.html' %}

{% block title %}Rapport des Utilisateurs - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-users me-2"></i>Rapport des Utilisateurs
    </h1>
    <div>
        <button onclick="window.print()" class="btn btn-primary me-2">
            <i class="fas fa-print me-1"></i>Imprimer
        </button>
        <a href="{{ url_for('users.index') }}" class="btn btn-secondary no-print">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Liste des utilisateurs</h5>
            <div class="text-muted">
                Date d'impression: {{ now.strftime('%d/%m/%Y %H:%M') }}
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Nom d'utilisateur</th>
                        <th>Nom complet</th>
                        <th>Email</th>
                        <th>Rôle</th>
                        <th>Date de création</th>
                        <th>Dernière connexion</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.full_name or '-' }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.is_admin %}
                            Administrateur
                            {% else %}
                            Utilisateur
                            {% endif %}
                        </td>
                        <td>{{ user.created_at.strftime('%d/%m/%Y') }}</td>
                        <td>
                            {% if user.last_login %}
                            {{ user.last_login.strftime('%d/%m/%Y %H:%M') }}
                            {% else %}
                            Jamais connecté
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer text-muted">
        Total: {{ users|length }} utilisateur(s)
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Résumé des permissions</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Utilisateur</th>
                        <th>Produits</th>
                        <th>Stock</th>
                        <th>Clients</th>
                        <th>Devis</th>
                        <th>Factures</th>
                        <th>Bons de livraison</th>
                        <th>Rapports</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>
                            {% if user.can_view_products %}Voir{% endif %}
                            {% if user.can_view_products and user.can_edit_products %}, {% endif %}
                            {% if user.can_edit_products %}Modifier{% endif %}
                        </td>
                        <td>
                            {% if user.can_view_stock %}Voir{% endif %}
                            {% if user.can_view_stock and user.can_edit_stock %}, {% endif %}
                            {% if user.can_edit_stock %}Modifier{% endif %}
                        </td>
                        <td>
                            {% if user.can_view_clients %}Voir{% endif %}
                            {% if user.can_view_clients and user.can_edit_clients %}, {% endif %}
                            {% if user.can_edit_clients %}Modifier{% endif %}
                        </td>
                        <td>
                            {% if user.can_view_quotes %}Voir{% endif %}
                            {% if user.can_view_quotes and user.can_edit_quotes %}, {% endif %}
                            {% if user.can_edit_quotes %}Modifier{% endif %}
                        </td>
                        <td>
                            {% if user.can_view_invoices %}Voir{% endif %}
                            {% if user.can_view_invoices and user.can_edit_invoices %}, {% endif %}
                            {% if user.can_edit_invoices %}Modifier{% endif %}
                        </td>
                        <td>
                            {% if user.can_view_delivery_notes %}Voir{% endif %}
                            {% if user.can_view_delivery_notes and user.can_edit_delivery_notes %}, {% endif %}
                            {% if user.can_edit_delivery_notes %}Modifier{% endif %}
                        </td>
                        <td>
                            {% if user.can_print_reports %}Oui{% else %}Non{% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
    @media print {
        .no-print {
            display: none !important;
        }
        
        body {
            padding: 0;
            margin: 0;
        }
        
        .card {
            border: none;
        }
        
        .card-header, .card-footer {
            background-color: transparent !important;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        
        thead {
            background-color: #f2f2f2;
        }
    }
</style>
{% endblock %}
