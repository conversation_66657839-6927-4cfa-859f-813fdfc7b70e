from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Email, Optional, Length

class SupplierForm(FlaskForm):
    name = StringField('Nom du fournisseur', validators=[DataRequired(), Length(min=2, max=100)],
                      render_kw={"placeholder": "Ex: Société ABC SARL"})
    
    contact_person = StringField('Personne de contact', validators=[Optional(), Length(max=100)],
                                render_kw={"placeholder": "Ex: <PERSON>"})
    
    email = StringField('Email', validators=[Optional(), Email()],
                       render_kw={"placeholder": "<EMAIL>"})
    
    phone = StringField('Téléphone', validators=[Optional(), Length(max=20)],
                       render_kw={"placeholder": "Ex: +212 6 12 34 56 78"})
    
    address = TextAreaField('Adresse', validators=[Optional()],
                           render_kw={"placeholder": "Adresse complète du fournisseur", "rows": 3})
    
    city = StringField('Ville', validators=[Optional(), Length(max=50)],
                      render_kw={"placeholder": "Ex: Casablanca"})
    
    postal_code = StringField('Code postal', validators=[Optional(), Length(max=20)],
                             render_kw={"placeholder": "Ex: 20000"})
    
    # Informations fiscales
    tax_id = StringField('Identifiant Fiscal (IF)', validators=[Optional(), Length(max=50)],
                        render_kw={"placeholder": "Ex: 12345678"})
    
    rc = StringField('Registre de Commerce (RC)', validators=[Optional(), Length(max=50)],
                    render_kw={"placeholder": "Ex: 123456"})
    
    ice = StringField('Identifiant Commun de l\'Entreprise (ICE)', validators=[Optional(), Length(max=50)],
                     render_kw={"placeholder": "Ex: 001234567890123"})
    
    submit = SubmitField('Enregistrer')
