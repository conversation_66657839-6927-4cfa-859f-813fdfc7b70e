{% extends 'base.html' %}

{% block title %}Gestion des Utilisateurs - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-users me-2"></i>Gestion des Utilisateurs
    </h1>
    <div>
        <a href="{{ url_for('users.create') }}" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i>Ajouter un utilisateur
        </a>
        <a href="{{ url_for('users.report') }}" class="btn btn-info ms-2">
            <i class="fas fa-print me-1"></i>Imprimer le rapport
        </a>
    </div>
</div>

{% if users %}
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Nom d'utilisateur</th>
                        <th>Nom complet</th>
                        <th>Email</th>
                        <th>Rôle</th>
                        <th>Dernière connexion</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.full_name or '-' }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.is_admin %}
                            <span class="badge bg-danger">Administrateur</span>
                            {% else %}
                            <span class="badge bg-secondary">Utilisateur</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.last_login %}
                            {{ user.last_login.strftime('%d/%m/%Y %H:%M') }}
                            {% else %}
                            Jamais connecté
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('users.show', id=user.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('users.edit', id=user.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Confirmer la suppression</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Êtes-vous sûr de vouloir supprimer l'utilisateur <strong>{{ user.username }}</strong> ?</p>
                                            <p class="text-danger">Cette action est irréversible.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                            <form action="{{ url_for('users.delete', id=user.id) }}" method="post">
                                                <button type="submit" class="btn btn-danger">Supprimer</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-info">
    <i class="fas fa-info-circle me-2"></i>Aucun utilisateur n'a été créé.
    <a href="{{ url_for('users.create') }}" class="btn btn-sm btn-primary ms-2">
        <i class="fas fa-plus-circle me-1"></i>Ajouter un utilisateur
    </a>
</div>
{% endif %}
{% endblock %}
