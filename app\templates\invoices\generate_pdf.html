<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture N° {{ invoice.invoice_number }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @page {
            size: A4 portrait;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: white;
        }

        .document-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background-color: white;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .content-wrapper {
            flex: 1;
        }

        /* Header rouge avec logo et titre */
        .header {
            background-color: #dc3545;
            color: white;
            height: 80px;
            display: flex;
            align-items: center;
            padding: 0 30px;
            margin-bottom: 0;
        }

        .logo-container {
            width: 200px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            margin-right: 30px;
            overflow: hidden;
        }

        .logo-container img {
            max-width: 200px;
            max-height: 80px;
            object-fit: contain;
        }

        .document-title {
            font-size: 36pt;
            font-weight: bold;
            flex: 1;
            text-align: center;
            margin: 0;
        }

        /* Section Client avec fond gris */
        .client-section {
            background-color: #f8f9fa;
            padding: 15px 30px;
            margin-bottom: 0;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
        }

        .client-info, .document-info {
            flex: 1;
        }

        .document-info {
            margin-left: 50px;
        }

        .info-line {
            margin-bottom: 5px;
            font-size: 11pt;
            display: flex;
            align-items: center;
        }

        .info-label {
            color: #666;
            display: inline-block;
            min-width: 120px;
            font-weight: bold;
        }

        .info-value {
            color: #333;
            flex: 1;
            margin-left: 10px;
        }

        /* Section Objet */
        .object-section {
            padding: 15px 30px;
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 12pt;
        }

        .object-value {
            border-bottom: 1px solid #dc3545;
            padding: 5px 0;
            color: #333;
            font-size: 11pt;
        }

        /* Tableau des articles */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 30px;
            width: calc(100% - 60px);
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
            height: 35px;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .items-table tr:nth-child(odd) {
            background-color: white;
        }

        .description-col {
            text-align: left !important;
            padding-left: 15px !important;
        }

        /* Section des totaux */
        .totals-section {
            margin: 30px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            border-collapse: collapse;
            min-width: 200px;
        }

        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #ddd;
            font-size: 11pt;
        }

        .totals-table .label-col {
            text-align: right;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .totals-table .value-col {
            text-align: right;
            min-width: 100px;
        }

        .total-ttc {
            background-color: #dc3545 !important;
            color: white !important;
            font-weight: bold !important;
        }

        /* Footer */
        .footer {
            margin-top: auto;
            padding: 20px 30px;
            text-align: center;
            font-size: 10pt;
            color: #666;
            border-top: 1px solid #ddd;
            background-color: #f8f9fa;
        }

        /* Boutons d'action pour l'écran */
        .action-buttons {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            margin: 3px;
            padding: 8px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-cancel {
            background: #6c757d;
            color: white;
        }

        /* إخفاء عناصر عند الطباعة */
        @media print {
            .action-buttons {
                display: none !important;
            }

            body {
                background: white !important;
            }

            .footer {
                position: static;
                margin-top: 50px;
            }

            /* الحفاظ على الألوان عند الطباعة */
            .header {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .items-table th {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .total-ttc {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .client-section {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .items-table tr:nth-child(even) {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }

            .footer {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
        }
    </style>
</head>
<body>
    <div class="action-buttons">
        <button class="btn btn-print" onclick="window.print()">
            <i class="fas fa-print"></i> Imprimer
        </button>
        <button class="btn btn-cancel" onclick="window.close()">
            <i class="fas fa-times"></i> Fermer
        </button>
    </div>

    <div class="document-container">
        <div class="content-wrapper">
            <!-- Header rouge avec logo et titre -->
            <div class="header">
                <div class="logo-container">
                    {% if company and company.logo %}
                        <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
                    {% else %}
                        Logo
                    {% endif %}
                </div>
                <div class="document-title">Facture</div>
            </div>

            <!-- Section Client avec fond gris -->
            <div class="client-section">
                <div class="client-title">Client :</div>
                <div class="client-details">
                    <div class="client-info">
                        <div class="info-line">
                            <span class="info-label">Nom du client :</span>
                            <span class="info-value">{{ invoice.client.name }}</span>
                        </div>
                        <div class="info-line">
                            <span class="info-label">Ice :</span>
                            <span class="info-value">{{ invoice.client.ice or '-' }}</span>
                        </div>
                        <div class="info-line">
                            <span class="info-label">Téléphone :</span>
                            <span class="info-value">{{ invoice.client.phone or '-' }}</span>
                        </div>
                        <div class="info-line">
                            <span class="info-label">Email :</span>
                            <span class="info-value">{{ invoice.client.email or '-' }}</span>
                        </div>
                    </div>
                    <div class="document-info">
                        <div class="info-line">
                            <span class="info-label">Date du Facture :</span>
                            <span class="info-value">{{ invoice.date.strftime('%d/%m/%Y') }}</span>
                        </div>
                        <div class="info-line">
                            <span class="info-label">Référence du Facture :</span>
                            <span class="info-value">{{ invoice.invoice_number }}</span>
                        </div>
                        <div class="info-line">
                            <span class="info-label">Date d'échéance :</span>
                            <span class="info-value">{{ invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else '-' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section Objet -->
            <div class="object-section">
                <div class="object-title">Objet :</div>
                <div class="object-value">{{ invoice.object or 'Facture de produits' }}</div>
            </div>

            <!-- Tableau des articles -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 35%;">Description</th>
                        <th style="width: 13%;">Unité</th>
                        <th style="width: 13%;">Quantité</th>
                        <th style="width: 13%;">Prix U.HT</th>
                        <th style="width: 13%;">Prix T.HT</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td class="description-col">{{ item.description }}</td>
                        <td>{{ item.unit or 'Unité' }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ "%.2f"|format(item.unit_price) }} DH</td>
                        <td>{{ "%.2f"|format(item.quantity * item.unit_price) }} DH</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Section des totaux -->
            <div class="totals-section">
                <table class="totals-table">
                    <tr>
                        <td class="label-col">Total HT</td>
                        <td class="value-col">{{ "%.2f"|format(invoice.subtotal) }} DH</td>
                    </tr>
                    <tr>
                        <td class="label-col">Total TVA ({{ invoice.tax_rate }}%)</td>
                        <td class="value-col">{{ "%.2f"|format(invoice.tax_amount) }} DH</td>
                    </tr>
                    <tr>
                        <td class="label-col total-ttc">Total TTC</td>
                        <td class="value-col total-ttc">{{ "%.2f"|format(invoice.total) }} DH</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% elif company %}
                {% if company.name %}{{ company.name }}{% endif %}
                {% if company.address %} - {{ company.address }}{% endif %}
                {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
                {% if company.email %} - Email: {{ company.email }}{% endif %}
                {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
            {% endif %}
        </div>
    </div>
</body>
</html>
