"""Add delivery_note_id to StockMovement

Revision ID: simple_delivery_note
Revises: 384c787f4ba8
Create Date: 2025-06-14 11:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'simple_delivery_note'
down_revision = '384c787f4ba8'
branch_labels = None
depends_on = None


def upgrade():
    # Add delivery_note_id column to stock_movement table
    with op.batch_alter_table('stock_movement', schema=None) as batch_op:
        batch_op.add_column(sa.Column('delivery_note_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_stock_movement_delivery_note', 'delivery_note', ['delivery_note_id'], ['id'])


def downgrade():
    # Drop foreign key constraint and column
    with op.batch_alter_table('stock_movement', schema=None) as batch_op:
        batch_op.drop_constraint('fk_stock_movement_delivery_note', type_='foreignkey')
        batch_op.drop_column('delivery_note_id')
