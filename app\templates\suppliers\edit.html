{% extends "base.html" %}

{% block title %}Modifier {{ supplier.name }} - Fournisseur{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-edit me-2"></i>Modifier {{ supplier.name }}
                </h1>
                <div>
                    <a href="{{ url_for('suppliers.view', id=supplier.id) }}" class="btn btn-info me-2">
                        <i class="fas fa-eye me-2"></i>Voir
                    </a>
                    <a href="{{ url_for('suppliers.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Modifier les informations du fournisseur</h6>
                </div>
                <div class="card-body">
                    <form method="POST" novalidate>
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <!-- Informations générales -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i>Informations générales
                                </h6>
                                
                                <div class="mb-3">
                                    {{ form.name.label(class="form-label") }} <span class="text-danger">*</span>
                                    {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                    {% if form.name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.contact_person.label(class="form-label") }}
                                    {{ form.contact_person(class="form-control" + (" is-invalid" if form.contact_person.errors else "")) }}
                                    {% if form.contact_person.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.contact_person.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.email.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Adresse et informations fiscales -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-map-marker-alt me-2"></i>Adresse
                                </h6>
                                
                                <div class="mb-3">
                                    {{ form.address.label(class="form-label") }}
                                    {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else "")) }}
                                    {% if form.address.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.address.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            {{ form.city.label(class="form-label") }}
                                            {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                                            {% if form.city.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.city.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            {{ form.postal_code.label(class="form-label") }}
                                            {{ form.postal_code(class="form-control" + (" is-invalid" if form.postal_code.errors else "")) }}
                                            {% if form.postal_code.errors %}
                                                <div class="invalid-feedback">
                                                    {% for error in form.postal_code.errors %}{{ error }}{% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <h6 class="text-primary mb-3 mt-4">
                                    <i class="fas fa-file-invoice me-2"></i>Informations fiscales
                                </h6>

                                <div class="mb-3">
                                    {{ form.tax_id.label(class="form-label") }}
                                    {{ form.tax_id(class="form-control" + (" is-invalid" if form.tax_id.errors else "")) }}
                                    {% if form.tax_id.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.tax_id.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.rc.label(class="form-label") }}
                                    {{ form.rc(class="form-control" + (" is-invalid" if form.rc.errors else "")) }}
                                    {% if form.rc.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.rc.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    {{ form.ice.label(class="form-label") }}
                                    {{ form.ice(class="form-control" + (" is-invalid" if form.ice.errors else "")) }}
                                    {% if form.ice.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.ice.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">
                        
                        <div class="d-flex justify-content-end">
                            <a href="{{ url_for('suppliers.view', id=supplier.id) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>Annuler
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
