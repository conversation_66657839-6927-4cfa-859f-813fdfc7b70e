{% extends 'base.html' %}

{% block title %}Modifier Facture {{ invoice.invoice_number }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice-dollar me-2"></i>Modifier Facture {{ invoice.invoice_number }}
    </h1>
    <div>
        <a href="{{ url_for('invoices.show', id=invoice.id) }}" class="btn btn-info me-2">
            <i class="fas fa-eye me-1"></i>Voir
        </a>
        <a href="{{ url_for('invoices.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Informations de la facture</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('invoices.edit', id=invoice.id) }}">
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        <label for="client_id" class="form-label required">Client</label>
                        {{ form.client_id(class="form-select") }}
                        {% if form.client_id.errors %}
                            <div class="text-danger">
                                {% for error in form.client_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label required">Statut</label>
                        {{ form.status(class="form-select") }}
                        {% if form.status.errors %}
                            <div class="text-danger">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="date" class="form-label">Date</label>
                        {{ form.date(class="form-control", type="date") }}
                        {% if form.date.errors %}
                            <div class="text-danger">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="due_date" class="form-label">Date d'échéance</label>
                        {{ form.due_date(class="form-control", type="date") }}
                        {% if form.due_date.errors %}
                            <div class="text-danger">
                                {% for error in form.due_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="tax_rate" class="form-label required">Taux de TVA (%)</label>
                        {{ form.tax_rate(class="form-control") }}
                        {% if form.tax_rate.errors %}
                            <div class="text-danger">
                                {% for error in form.tax_rate.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        {{ form.notes(class="form-control", rows=3) }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Produits</h5>
                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addItemModal">
                    <i class="fas fa-plus-circle me-1"></i>Ajouter un produit
                </button>
            </div>
            <div class="card-body">
                {% if invoice.items.all() %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Référence</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                                <th>Total</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in invoice.items %}
                            <tr>
                                <td>{{ item.product.name }}</td>
                                <td>{{ item.product.reference }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.unit_price }} MAD</td>
                                <td>{{ item.total }} MAD</td>
                                <td>
                                    <form action="{{ url_for('invoices.remove_item', invoice_id=invoice.id, item_id=item.id) }}" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet article ?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <td colspan="4" class="text-end fw-bold">Sous-total:</td>
                                <td class="fw-bold">{{ invoice.subtotal }} MAD</td>
                                <td></td>
                            </tr>
                            <tr class="table-light">
                                <td colspan="4" class="text-end fw-bold">TVA ({{ invoice.tax_rate }}%):</td>
                                <td class="fw-bold">{{ invoice.tax_amount }} MAD</td>
                                <td></td>
                            </tr>
                            <tr class="table-dark">
                                <td colspan="4" class="text-end fw-bold">Total:</td>
                                <td class="fw-bold">{{ invoice.total }} MAD</td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Aucun produit n'a été ajouté à cette facture.
                    <button type="button" class="btn btn-sm btn-success ms-2" data-bs-toggle="modal" data-bs-target="#addItemModal">
                        <i class="fas fa-plus-circle me-1"></i>Ajouter un produit
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        {% if invoice.items.all() and invoice.status != 'draft' %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Gestion du stock</h5>
            </div>
            <div class="card-body">
                <p>Vous pouvez créer des mouvements de stock pour les produits de cette facture.</p>
                <form action="{{ url_for('invoices.create_stock_movement', id=invoice.id) }}" method="post">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-boxes me-1"></i>Créer les mouvements de stock
                    </button>
                </form>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal pour ajouter un produit -->
<div class="modal fade" id="addItemModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un produit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{{ url_for('invoices.add_item', id=invoice.id) }}">
                {{ item_form.hidden_tag() }}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="product_id" class="form-label required">Produit</label>
                        {{ item_form.product_id(class="form-select") }}
                        {% if item_form.product_id.errors %}
                            <div class="text-danger">
                                {% for error in item_form.product_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="quantity" class="form-label required">Quantité</label>
                        {{ item_form.quantity(class="form-control", value="1") }}
                        {% if item_form.quantity.errors %}
                            <div class="text-danger">
                                {% for error in item_form.quantity.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="unit_price" class="form-label">Prix unitaire (€)</label>
                        {{ item_form.unit_price(class="form-control", placeholder="Laisser vide pour utiliser le prix par défaut") }}
                        {% if item_form.unit_price.errors %}
                            <div class="text-danger">
                                {% for error in item_form.unit_price.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Si non spécifié, le prix unitaire du produit sera utilisé</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    {{ item_form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
