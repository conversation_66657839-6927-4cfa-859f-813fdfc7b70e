from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from app import db
from app.models import Invoice, InvoiceItem, Product, Client, Quote, QuoteItem, StockMovement
from app.forms.invoice_forms import InvoiceForm, InvoiceItemForm
from datetime import datetime, timedelta
import random
import string

bp = Blueprint('invoices', __name__)

def generate_invoice_number():
    prefix = "FACT-"
    date_part = datetime.now().strftime("%Y%m")
    random_part = ''.join(random.choices(string.digits, k=4))
    return f"{prefix}{date_part}-{random_part}"

@bp.route('/')
def index():
    invoices = Invoice.query.order_by(Invoice.date.desc()).all()
    return render_template('invoices/index.html', invoices=invoices)

@bp.route('/create', methods=['GET', 'POST'])
def create():
    form = InvoiceForm()

    # Populate client choices
    form.client_id.choices = [(c.id, c.name) for c in Client.query.all()]

    # Populate quote choices (optional)
    quotes = Quote.query.filter_by(status='accepted').all()
    form.quote_id.choices = [(0, 'None')] + [(q.id, f"{q.quote_number} - {q.client.name}") for q in quotes]

    if form.validate_on_submit():
        invoice = Invoice(
            invoice_number=generate_invoice_number(),
            client_id=form.client_id.data,
            date=form.date.data or datetime.utcnow(),
            due_date=form.due_date.data or (datetime.utcnow() + timedelta(days=30)),
            status=form.status.data,
            tax_rate=form.tax_rate.data,
            notes=form.notes.data
        )

        if form.quote_id.data and form.quote_id.data > 0:
            invoice.quote_id = form.quote_id.data
            quote = Quote.query.get(form.quote_id.data)

            # Copy items from quote
            for quote_item in quote.items:
                invoice_item = InvoiceItem(
                    product_id=quote_item.product_id,
                    quantity=quote_item.quantity,
                    unit_price=quote_item.unit_price,
                    total=quote_item.total
                )
                invoice.items.append(invoice_item)

            # Update quote status
            quote.status = 'invoiced'

        db.session.add(invoice)
        db.session.commit()

        # Calculate totals
        invoice.calculate_totals()
        db.session.commit()

        flash('Invoice created successfully!', 'success')
        return redirect(url_for('invoices.edit', id=invoice.id))

    # Get company information for the header
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    return render_template('invoices/create.html', form=form, company=company)

@bp.route('/create-interactive', methods=['GET', 'POST'])
def create_interactive():
    """Interactive invoice creation with document-like interface"""
    from datetime import datetime, timedelta
    import json

    if request.method == 'POST':
        # Handle AJAX request for saving invoice
        try:
            data = request.get_json()

            # Create new invoice
            invoice = Invoice(
                invoice_number=generate_invoice_number(),
                client_id=data.get('client_id'),
                date=datetime.strptime(data.get('date'), '%Y-%m-%d') if data.get('date') else datetime.utcnow(),
                due_date=datetime.strptime(data.get('due_date'), '%Y-%m-%d') if data.get('due_date') else (datetime.utcnow() + timedelta(days=30)),
                status='draft',
                tax_rate=20.0,
                notes=data.get('object', '')
            )

            db.session.add(invoice)
            db.session.flush()  # Get the invoice ID

            # Add items
            for item_data in data.get('items', []):
                item = InvoiceItem(
                    invoice_id=invoice.id,
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity'),
                    unit_price=item_data.get('unit_price'),
                    total=item_data.get('quantity') * item_data.get('unit_price')
                )
                db.session.add(item)

            db.session.commit()

            # Calculate totals
            invoice.calculate_totals()
            db.session.commit()

            return jsonify({'success': True, 'invoice_id': invoice.id})

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})

    # GET request - show the form
    clients = Client.query.all()

    # Get company information
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    # Generate invoice number and dates
    invoice_number = generate_invoice_number()
    today = datetime.now().strftime('%Y-%m-%d')
    due_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    return render_template('invoices/create_interactive.html',
                         clients=clients,
                         company=company,
                         invoice_number=invoice_number,
                         today=today,
                         due_date=due_date)

@bp.route('/<int:id>/edit-interactive', methods=['GET', 'POST'])
def edit_interactive(id):
    """Interactive invoice editing with document-like interface"""
    invoice = Invoice.query.get_or_404(id)

    if request.method == 'POST':
        # Handle AJAX request for updating invoice
        try:
            data = request.get_json()

            # Update invoice
            invoice.client_id = data.get('client_id')
            invoice.date = datetime.strptime(data.get('date'), '%Y-%m-%d') if data.get('date') else invoice.date
            invoice.due_date = datetime.strptime(data.get('due_date'), '%Y-%m-%d') if data.get('due_date') else invoice.due_date
            invoice.notes = data.get('object', '')

            # Delete existing items
            InvoiceItem.query.filter_by(invoice_id=invoice.id).delete()

            # Add new items
            for item_data in data.get('items', []):
                item = InvoiceItem(
                    invoice_id=invoice.id,
                    description=item_data.get('description'),
                    quantity=item_data.get('quantity'),
                    unit_price=item_data.get('unit_price'),
                    total=item_data.get('quantity') * item_data.get('unit_price')
                )
                db.session.add(item)

            db.session.commit()

            # Calculate totals
            invoice.calculate_totals()
            db.session.commit()

            return jsonify({'success': True, 'invoice_id': invoice.id})

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)})

    # GET request - show the form
    clients = Client.query.all()

    # Get company information
    try:
        from app.models.company import Company
        company = Company.query.first()
    except:
        company = None

    return render_template('invoices/edit_interactive.html',
                         invoice=invoice,
                         clients=clients,
                         company=company)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    invoice = Invoice.query.get_or_404(id)
    form = InvoiceForm(obj=invoice)
    item_form = InvoiceItemForm()

    # Populate client choices
    form.client_id.choices = [(c.id, c.name) for c in Client.query.all()]

    # Populate quote choices (optional)
    quotes = Quote.query.filter_by(status='accepted').all()
    form.quote_id.choices = [(0, 'None')] + [(q.id, f"{q.quote_number} - {q.client.name}") for q in quotes]

    # Populate product choices for the item form
    item_form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        invoice.client_id = form.client_id.data
        invoice.date = form.date.data
        invoice.due_date = form.due_date.data
        invoice.status = form.status.data
        invoice.tax_rate = form.tax_rate.data
        invoice.notes = form.notes.data

        db.session.commit()
        invoice.calculate_totals()
        db.session.commit()

        flash('Invoice updated successfully!', 'success')
        return redirect(url_for('invoices.edit', id=invoice.id))

    return render_template('invoices/edit.html', invoice=invoice, form=form, item_form=item_form)

@bp.route('/<int:id>/add_item', methods=['POST'])
def add_item(id):
    invoice = Invoice.query.get_or_404(id)
    form = InvoiceItemForm()

    # Populate product choices
    form.product_id.choices = [(p.id, f"{p.name} ({p.reference})") for p in Product.query.all()]

    if form.validate_on_submit():
        product = Product.query.get(form.product_id.data)

        item = InvoiceItem(
            invoice_id=invoice.id,
            product_id=form.product_id.data,
            quantity=form.quantity.data,
            unit_price=form.unit_price.data or product.unit_price,
            total=form.quantity.data * (form.unit_price.data or product.unit_price)
        )

        db.session.add(item)
        db.session.commit()

        # Recalculate invoice totals
        invoice.calculate_totals()
        db.session.commit()

        flash('Item added to invoice!', 'success')
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f"{getattr(form, field).label.text}: {error}", 'danger')

    return redirect(url_for('invoices.edit', id=invoice.id))

@bp.route('/<int:invoice_id>/remove_item/<int:item_id>', methods=['POST'])
def remove_item(invoice_id, item_id):
    item = InvoiceItem.query.get_or_404(item_id)
    invoice = Invoice.query.get_or_404(invoice_id)

    db.session.delete(item)
    db.session.commit()

    # Recalculate invoice totals
    invoice.calculate_totals()
    db.session.commit()

    flash('Item removed from invoice!', 'success')
    return redirect(url_for('invoices.edit', id=invoice_id))

@bp.route('/<int:id>')
def show(id):
    """Show invoice using the same interactive template as create/edit"""
    invoice = Invoice.query.get_or_404(id)

    try:
        from app.models.company import Company
        from app.models import Client

        # Get company information
        company = Company.query.first()

        # Get all clients for the dropdown
        clients = Client.query.all()

        # Render the interactive template (same as create/edit but read-only)
        return render_template('invoices/print_final.html',
                             invoice=invoice,
                             company=company,
                             clients=clients)
    except Exception as e:
        flash(f'Erreur lors de l\'affichage de la facture: {str(e)}', 'danger')
        return redirect(url_for('invoices.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    invoice = Invoice.query.get_or_404(id)
    db.session.delete(invoice)
    db.session.commit()
    flash('Invoice deleted successfully!', 'success')
    return redirect(url_for('invoices.index'))

@bp.route('/<int:id>/generate_pdf')
def generate_pdf(id):
    """Display HTML template for printing (same as create form)"""
    invoice = Invoice.query.get_or_404(id)

    try:
        from app.models.company import Company

        # Get company information
        company = Company.query.first()

        # Render the HTML template directly (same as interactive form)
        return render_template('invoices/print_final.html', invoice=invoice, company=company)

    except Exception as e:
        flash(f'Erreur lors de la génération du template: {str(e)}', 'danger')
        return redirect(url_for('invoices.show', id=invoice.id))

@bp.route('/<int:id>/view_pdf')
def view_pdf(id):
    """View PDF template in browser"""
    invoice = Invoice.query.get_or_404(id)

    try:
        from app.models.company import Company
        company = Company.query.first()
        return render_template('invoices/print_final.html', invoice=invoice, company=company)
    except Exception as e:
        flash(f'Erreur lors de la génération du template: {str(e)}', 'danger')
        return redirect(url_for('invoices.show', id=invoice.id))

@bp.route('/<int:id>/create_stock_movement', methods=['POST'])
def create_stock_movement(id):
    invoice = Invoice.query.get_or_404(id)

    # Create stock movements for each item
    for item in invoice.items:
        product = Product.query.get(item.product_id)

        # Check if there's enough stock
        if product.current_quantity < item.quantity:
            flash(f'Not enough stock for {product.name}. Current quantity: {product.current_quantity}', 'danger')
            return redirect(url_for('invoices.show', id=invoice.id))

        # Create stock movement
        movement = StockMovement(
            product_id=item.product_id,
            movement_type='OUT',
            quantity=item.quantity,
            notes=f"Invoice {invoice.invoice_number}",
            reference_document=invoice.invoice_number
        )

        # Update product quantity
        product.current_quantity -= item.quantity

        db.session.add(movement)

    db.session.commit()
    flash('Stock movements created successfully!', 'success')
    return redirect(url_for('invoices.show', id=invoice.id))
