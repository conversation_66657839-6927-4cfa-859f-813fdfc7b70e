<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture N° {{ invoice.invoice_number if invoice else '143' }}</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 0;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .page-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 0;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Header rouge avec logo et titre */
        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            height: 90px;
            display: flex;
            align-items: center;
            padding: 0 30px;
            margin-bottom: 0;
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.03) 10px,
                rgba(255,255,255,0.03) 20px
            );
        }

        .logo-container {
            width: 200px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10pt;
            margin-right: 30px;
            overflow: hidden;
        }

        .logo-container img {
            max-width: 200px;
            max-height: 80px;
            object-fit: contain;
        }

        .document-title {
            font-size: 36pt;
            font-weight: bold;
            flex: 1;
            text-align: center;
            margin: 0;
        }

        /* Section Client avec fond gris */
        .client-section {
            background-color: #f8f9fa;
            padding: 15px 30px;
            margin-bottom: 0;
        }

        .client-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
            font-size: 12pt;
        }

        .client-details {
            display: flex;
            justify-content: space-between;
        }

        .client-info {
            flex: 1;
        }

        .document-info {
            flex: 1;
            text-align: left;
            margin-left: 50px;
        }

        .info-line {
            margin-bottom: 5px;
            font-size: 11pt;
        }

        .info-label {
            color: #666;
            display: inline-block;
            min-width: 120px;
        }

        /* Section Objet */
        .object-section {
            padding: 15px 30px;
            margin-bottom: 20px;
        }

        .object-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            font-size: 12pt;
        }

        .object-content {
            font-size: 11pt;
            color: #333;
            min-height: 20px;
            padding: 5px 0;
            border-bottom: 1px solid #dc3545;
        }

        /* Tableau des articles */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0 30px;
            width: calc(100% - 60px);
        }

        .items-table th {
            background-color: #dc3545;
            color: white;
            padding: 12px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #dc3545;
            font-size: 11pt;
        }

        .items-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 11pt;
            height: 30px;
        }

        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .items-table tr:nth-child(odd) {
            background-color: white;
        }

        .description-col {
            text-align: left !important;
            padding-left: 15px !important;
        }

        /* Section des totaux */
        .totals-section {
            margin: 30px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            border-collapse: collapse;
            min-width: 200px;
        }

        .totals-table td {
            padding: 8px 15px;
            border: 1px solid #ddd;
            font-size: 11pt;
        }

        .totals-table .label-col {
            text-align: right;
            font-weight: bold;
            background-color: #f8f9fa;
        }

        .totals-table .value-col {
            text-align: right;
            min-width: 100px;
        }

        .total-ttc {
            background-color: #dc3545 !important;
            color: white !important;
            font-weight: bold !important;
        }

        /* Service info */
        .service-info {
            margin: 30px;
            font-size: 10pt;
            color: #666;
            font-style: italic;
        }

        /* Footer */
        .footer {
            position: fixed;
            bottom: 20px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10pt;
            color: #dc3545;
            font-weight: bold;
            padding: 10px 30px;
        }

        .no-print {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }

        .btn {
            padding: 8px 15px;
            margin: 3px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-weight: bold;
            font-size: 10pt;
        }

        .btn-print {
            background: #dc3545;
            color: white;
        }

        .btn-close {
            background: #6c757d;
            color: white;
        }

        @media print {
            .no-print {
                display: none !important;
            }

            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            html {
                margin: 0 !important;
                padding: 0 !important;
                width: 210mm !important;
                height: 297mm !important;
            }

            body {
                background: white !important;
                margin: 0 !important;
                padding: 0 !important;
                font-size: 11pt !important;
                width: 210mm !important;
                height: 297mm !important;
                transform: scale(1) !important;
                transform-origin: 0 0 !important;
            }

            .page-container {
                box-shadow: none !important;
                margin: 0 !important;
                padding: 0mm !important;
                width: 210mm !important;
                height: 297mm !important;
                max-width: 210mm !important;
                min-height: 297mm !important;
                box-sizing: border-box !important;
                position: relative !important;
                overflow: hidden !important;
            }

            /* Preserve colors in print */
            .header {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .client-section {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .total-ttc {
                background-color: #dc3545 !important;
                color: white !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            /* Reduce footer font size */
            .footer {
                font-size: 8pt !important;
                margin-top: 20px !important;
                padding: 10px !important;
                position: static;
            }

            /* Compact layout for single page */
            .header {
                padding: 8px 20px !important;
                margin-bottom: 10px !important;
            }

            .client-section {
                padding: 8px 15px !important;
                margin-bottom: 10px !important;
            }

            .object-section {
                margin-bottom: 10px !important;
            }

            .items-table {
                font-size: 10pt !important;
                margin-bottom: 10px !important;
            }

            .totals-section {
                margin-bottom: 10px !important;
            }

            /* Force single page */
            @page {
                size: A4 portrait;
                margin: 0mm;
            }

            .page-container {
                page-break-inside: avoid !important;
                page-break-after: avoid !important;
                page-break-before: avoid !important;
            }

            /* Compact layout for print - keep original design with internal margins */
            .header {
                height: 60px !important;
                padding: 0 15mm !important;
            }

            .client-section {
                padding: 10px 15mm !important;
            }

            .object-section {
                padding: 10px 15mm !important;
                margin-bottom: 10px !important;
            }

            .items-table {
                margin: 0 15mm !important;
                width: calc(100% - 30mm) !important;
                font-size: 9pt !important;
            }

            .totals-section {
                margin: 10px 15mm !important;
            }

            .footer {
                padding: 8px 15mm !important;
                font-size: 7pt !important;
                margin-top: 30px !important;
                position: absolute !important;
                bottom: 10mm !important;
                left: 0 !important;
                right: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- Boutons de contrôle -->
    <div class="no-print">
        <button class="btn btn-print" onclick="window.print()">Imprimer</button>
        <button class="btn btn-close" onclick="history.back()">Fermer</button>
    </div>

    <div class="page-container">
        <!-- Header rouge avec logo et titre -->
    <div class="header">
        <div class="logo-container">
            {% if company and company.logo %}
                <img src="{{ url_for('static', filename=company.logo) }}" alt="Logo">
            {% else %}
                Logo
            {% endif %}
        </div>
        <div class="document-title">Facture</div>
    </div>

    <!-- Section Client avec fond gris -->
    <div class="client-section">
        <div class="client-title">Client :</div>
        <div class="client-details">
            <div class="client-info">
                <div class="info-line">
                    <span class="info-label">Nom du client :</span>
                    {{ invoice.client.name if invoice and invoice.client else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Ice :</span>
                    {{ invoice.client.ice if invoice and invoice.client and invoice.client.ice else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Téléphone :</span>
                    {{ invoice.client.phone if invoice and invoice.client else '' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Email :</span>
                    {{ invoice.client.email if invoice and invoice.client else '' }}
                </div>
            </div>
            <div class="document-info">
                <div class="info-line">
                    <span class="info-label">Date du Facture :</span>
                    {{ invoice.date.strftime('%d.%m.%Y') if invoice and invoice.date else '1.6.2021' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Référence du Facture :</span>
                    {{ invoice.invoice_number if invoice else '143' }}
                </div>
                <div class="info-line">
                    <span class="info-label">Date d'échéance :</span>
                    {{ invoice.due_date.strftime('%d.%m.%Y') if invoice and invoice.due_date else '15.6.2021' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Objet -->
    <div class="object-section">
        <div class="object-title">Objet :</div>
        <div class="object-content">{{ invoice.notes if invoice and invoice.notes else '' }}</div>
    </div>

    <!-- Tableau des articles -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 40%;">Description</th>
                <th style="width: 15%;">Unité</th>
                <th style="width: 15%;">Quantité</th>
                <th style="width: 15%;">Prix U.HT</th>
                <th style="width: 15%;">Prix T.HT</th>
            </tr>
        </thead>
        <tbody>
            {% if invoice and invoice.items %}
                {% for item in invoice.items %}
                <tr>
                    <td class="description-col">{{ item.product.name if item.product else (item.description or '') }}</td>
                    <td>Unité</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ "%.2f"|format(item.unit_price) }}</td>
                    <td>{{ "%.2f"|format(item.total) }}</td>
                </tr>
                {% endfor %}
                <!-- Lignes vides pour remplir l'espace -->
                {% set items_count = invoice.items|list|length if invoice and invoice.items else 0 %}
                {% for i in range(8 - items_count) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>0,00</td>
                    <td>0,00</td>
                </tr>
                {% endfor %}
            {% else %}
                <!-- Lignes vides par défaut -->
                {% for i in range(8) %}
                <tr>
                    <td class="description-col">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>0,00</td>
                    <td>0,00</td>
                </tr>
                {% endfor %}
            {% endif %}
        </tbody>
    </table>

    <!-- Section des totaux -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label-col">Total HT</td>
                <td class="value-col">{{ "%.2f"|format(invoice.subtotal) if invoice else '0,00' }} DH</td>
            </tr>
            <tr>
                <td class="label-col">Total TVA (20%)</td>
                <td class="value-col">{{ "%.2f"|format(invoice.tax_amount) if invoice and invoice.tax_amount else '0,00' }} DH</td>
            </tr>
            <tr>
                <td class="label-col total-ttc">Total TTC</td>
                <td class="value-col total-ttc">{{ "%.2f"|format(invoice.total) if invoice else '0,00' }} DH</td>
            </tr>
        </table>
    </div>



        <!-- Footer -->
        <div class="footer">
            {% if company and company.footer_text %}
                {{ company.footer_text }}
            {% elif company %}
                {% if company.name %}{{ company.name }}{% endif %}
                {% if company.address %} - {{ company.address }}{% endif %}
                {% if company.phone %} - Tél: {{ company.phone }}{% endif %}
                {% if company.email %} - Email: {{ company.email }}{% endif %}
                {% if company.ice %} - ICE: {{ company.ice }}{% endif %}
            {% endif %}
        </div>
    </div>
</body>
</html>
