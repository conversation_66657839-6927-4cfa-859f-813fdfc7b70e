{% extends 'base.html' %}

{% block title %}Devis {{ quote.quote_number }} - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-invoice me-2"></i>Devis {{ quote.quote_number }}
    </h1>
    <div>
        <a href="{{ url_for('quotes.generate_pdf', id=quote.id) }}" class="btn btn-primary me-2">
            <i class="fas fa-file-pdf me-1"></i>Générer PDF
        </a>
        <a href="{{ url_for('quotes.edit', id=quote.id) }}" class="btn btn-warning me-2">
            <i class="fas fa-edit me-1"></i>Modifier
        </a>
        <a href="{{ url_for('quotes.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>Retour à la liste
        </a>
    </div>
</div>

<div class="card mb-4 print-friendly">
    <div class="card-header">
        <div class="row">
            <div class="col-md-6">
                <h5 class="card-title mb-0">Devis N° {{ quote.quote_number }}</h5>
            </div>
            <div class="col-md-6 text-md-end">
                <span class="badge
                    {% if quote.status == 'draft' %}bg-secondary
                    {% elif quote.status == 'sent' %}bg-info
                    {% elif quote.status == 'accepted' %}bg-success
                    {% elif quote.status == 'rejected' %}bg-danger
                    {% endif %}">
                    {% if quote.status == 'draft' %}Brouillon
                    {% elif quote.status == 'sent' %}Envoyé
                    {% elif quote.status == 'accepted' %}Accepté
                    {% elif quote.status == 'rejected' %}Refusé
                    {% endif %}
                </span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="fw-bold">Informations du devis</h6>
                <p>
                    <strong>Date:</strong> {{ quote.date.strftime('%d/%m/%Y') }}<br>
                    <strong>Date d'expiration:</strong> {{ quote.expiration_date.strftime('%d/%m/%Y') if quote.expiration_date else 'Non spécifiée' }}<br>
                    <strong>Taux de TVA:</strong> {{ quote.tax_rate }}%
                </p>
            </div>
            <div class="col-md-6">
                <h6 class="fw-bold">Client</h6>
                <p>
                    <strong>{{ quote.client.name }}</strong><br>
                    {% if quote.client.address %}{{ quote.client.address }}<br>{% endif %}
                    {% if quote.client.postal_code or quote.client.city %}
                        {% if quote.client.postal_code %}{{ quote.client.postal_code }}{% endif %}
                        {% if quote.client.city %}{{ quote.client.city }}{% endif %}<br>
                    {% endif %}
                    {% if quote.client.email %}Email: {{ quote.client.email }}<br>{% endif %}
                    {% if quote.client.phone %}Tél: {{ quote.client.phone }}{% endif %}
                </p>
            </div>
        </div>

        <h6 class="fw-bold mb-3">Produits</h6>
        {% if quote.items.all() %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Référence</th>
                        <th>Quantité</th>
                        <th>Prix unitaire</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in quote.items %}
                    <tr>
                        <td>{{ item.product.name }}</td>
                        <td>{{ item.product.reference }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.unit_price }} MAD</td>
                        <td>{{ item.total }} MAD</td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-light">
                        <td colspan="4" class="text-end fw-bold">Sous-total:</td>
                        <td class="fw-bold">{{ quote.subtotal }} MAD</td>
                    </tr>
                    <tr class="table-light">
                        <td colspan="4" class="text-end fw-bold">TVA ({{ quote.tax_rate }}%):</td>
                        <td class="fw-bold">{{ quote.tax_amount }} MAD</td>
                    </tr>
                    <tr class="table-dark">
                        <td colspan="4" class="text-end fw-bold">Total:</td>
                        <td class="fw-bold">{{ quote.total }} MAD</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>Aucun produit n'a été ajouté à ce devis.
            <a href="{{ url_for('quotes.edit', id=quote.id) }}" class="btn btn-sm btn-success ms-2">
                <i class="fas fa-plus-circle me-1"></i>Ajouter des produits
            </a>
        </div>
        {% endif %}

        {% if quote.notes %}
        <div class="mt-4">
            <h6 class="fw-bold">Notes</h6>
            <p>{{ quote.notes }}</p>
        </div>
        {% endif %}
    </div>
    <div class="card-footer">
        <div class="row">
            <div class="col-md-6">
                <small class="text-muted">Créé le {{ quote.date.strftime('%d/%m/%Y') }}</small>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="btn-group no-print">
                    {% if quote.status == 'draft' %}
                    <form action="#" method="post" class="d-inline">
                        <input type="hidden" name="status" value="sent">
                        <button type="submit" class="btn btn-sm btn-info">
                            <i class="fas fa-paper-plane me-1"></i>Marquer comme envoyé
                        </button>
                    </form>
                    {% elif quote.status == 'sent' %}
                    <form action="#" method="post" class="d-inline me-2">
                        <input type="hidden" name="status" value="accepted">
                        <button type="submit" class="btn btn-sm btn-success">
                            <i class="fas fa-check me-1"></i>Marquer comme accepté
                        </button>
                    </form>
                    <form action="#" method="post" class="d-inline">
                        <input type="hidden" name="status" value="rejected">
                        <button type="submit" class="btn btn-sm btn-danger">
                            <i class="fas fa-times me-1"></i>Marquer comme refusé
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4 no-print">
    <div class="card-header">
        <h5 class="card-title mb-0">Actions</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('quotes.edit', id=quote.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier le devis
                    </a>
                    <a href="{{ url_for('quotes.generate_pdf', id=quote.id) }}" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-2"></i>Générer PDF
                    </a>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-grid gap-2">
                    {% if quote.status == 'accepted' %}
                    <a href="#" class="btn btn-success">
                        <i class="fas fa-file-invoice-dollar me-2"></i>Créer une facture
                    </a>
                    {% endif %}
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                        <i class="fas fa-trash me-2"></i>Supprimer le devis
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade delete-modal" id="deleteModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer le devis <strong>{{ quote.quote_number }}</strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form action="{{ url_for('quotes.delete', id=quote.id) }}" method="post">
                    <button type="submit" class="btn btn-delete-confirm">
                        <i class="fas fa-trash-alt me-2"></i>Supprimer Devis
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
