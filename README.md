# تطبيق إدارة طفايات الحريق والـ RIA

تطبيق ويب شامل لإدارة منتجات طفايات الحريق والـ RIA، يشمل إدارة المخزون والوظائف التجارية.

## المميزات الرئيسية

- **إدارة المنتجات** : طفايات الحريق، أنظمة RIA
- **إدارة المخزون** : الإدخالات، الإخراجات، تتبع الكميات
- **الإدارة التجارية** : الديفيسات، الفواتير، بونات التسليم
- **إدارة العملاء** : معلومات العملاء وتاريخ المعاملات
- **إدارة الشركة** : معلومات الشركة والشعار
- **إدارة المستخدمين** : صلاحيات متعددة المستويات

## التقنيات المستخدمة

- **الخادم الخلفي** : Python مع Flask
- **قاعدة البيانات** : SQLite (قابل للترقية إلى PostgreSQL)
- **الواجهة الأمامية** : HTML, Bootstrap, JavaScript
- **الطباعة** : قوالب PDF محسنة للطباعة A4 أفقي

## التثبيت والإعداد

1. استنساخ المستودع:
```bash
git clone <url-du-depot>
cd app-gestion-extincteur
```

2. إنشاء بيئة افتراضية وتفعيلها:
```bash
python -m venv venv
source venv/bin/activate  # على Windows: venv\Scripts\activate
```

3. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

4. تهيئة قاعدة البيانات:
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

5. تشغيل التطبيق:
```bash
python run.py
```

## التحسينات الجديدة

### تحسينات الطباعة
- ✅ تصميم A4 أفقي محسن
- ✅ هوامش وتخطيط محسن
- ✅ ملف CSS مشترك للطباعة
- ✅ دعم الطباعة الملونة والأبيض والأسود

### تحسينات الواجهة
- ✅ تحويل القوالب للغة العربية
- ✅ تصميم أحمر موحد
- ✅ عرض الأسعار بالدرهم المغربي
- ✅ تحسين تخطيط الجداول

### تنظيف الكود
- ✅ حذف الملفات المتكررة والزائدة
- ✅ توحيد نماذج البيانات
- ✅ تحسين بنية المشروع

## هيكل المشروع

```
extincteurs_app/
├── app/
│   ├── static/
│   │   ├── css/
│   │   │   ├── style.css
│   │   │   └── print.css (جديد - للطباعة المحسنة)
│   │   ├── js/
│   │   └── uploads/
│   ├── templates/
│   │   ├── pdf/ (قوالب الطباعة المحسنة)
│   │   │   ├── quote.html
│   │   │   ├── invoice.html
│   │   │   └── delivery_note.html
│   │   ├── quotes/
│   │   ├── invoices/
│   │   ├── delivery_notes/
│   │   └── base.html
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py
│   │   └── company.py
│   ├── routes/
│   │   ├── main.py
│   │   ├── quotes.py
│   │   ├── invoices.py
│   │   ├── delivery_notes.py
│   │   ├── products.py
│   │   ├── stock.py
│   │   ├── clients.py
│   │   ├── users.py
│   │   └── company.py
│   ├── forms/
│   └── __init__.py
├── migrations/
├── config.py
├── run.py
└── requirements.txt
```

## الاستخدام

1. الوصول للتطبيق عبر http://localhost:5000
2. ابدأ بإضافة المنتجات
3. إدارة المخزون بالإدخالات والإخراجات
4. إنشاء ديفيسات وفواتير وبونات تسليم للعملاء

## الوظائف التفصيلية

### إدارة المنتجات
- إضافة، تعديل، حذف المنتجات
- التصنيف (طفايات الحريق، أنظمة RIA)
- إدارة المراجع والأوصاف

### إدارة المخزون
- إدخالات المخزون مع المورد
- إخراجات المخزون مع السبب
- تتبع الكميات في الوقت الفعلي
- تنبيهات المخزون المنخفض

### الإدارة التجارية
- إنشاء ديفيسات مع حساب تلقائي للمجاميع
- تحويل الديفيسات إلى فواتير
- إنتاج بونات التسليم
- تتبع الحالات (مسودة، مرسل، مقبول، إلخ)

### الوظائف المتقدمة
- تصدير البيانات إلى Excel
- طباعة الوثائق بتصميم محسن A4 أفقي
- إدارة العملاء
- تاريخ الحركات

## معلومات الاتصال والدعم

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: 05 37 29 50 31

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**ملاحظة**: تم تحسين التطبيق ليدعم الطباعة بتنسيق A4 أفقي مع تصميم عربي محسن وألوان حمراء موحدة.
