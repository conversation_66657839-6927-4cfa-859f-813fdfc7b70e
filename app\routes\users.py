from flask import Blueprint, render_template, redirect, url_for, flash, request
from app import db
from app.models.user import User
from app.forms.user_forms import RegistrationForm, EditUserForm
from datetime import datetime

bp = Blueprint('users', __name__)

@bp.route('/')
def index():
    users = User.query.all()
    return render_template('users/index.html', users=users)

@bp.route('/create', methods=['GET', 'POST'])
def create():
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,
            full_name=form.full_name.data,
            is_admin=form.is_admin.data
        )
        
        # Set permissions
        user.can_view_products = form.can_view_products.data
        user.can_edit_products = form.can_edit_products.data
        user.can_view_stock = form.can_view_stock.data
        user.can_edit_stock = form.can_edit_stock.data
        user.can_view_clients = form.can_view_clients.data
        user.can_edit_clients = form.can_edit_clients.data
        user.can_view_quotes = form.can_view_quotes.data
        user.can_edit_quotes = form.can_edit_quotes.data
        user.can_view_invoices = form.can_view_invoices.data
        user.can_edit_invoices = form.can_edit_invoices.data
        user.can_view_delivery_notes = form.can_view_delivery_notes.data
        user.can_edit_delivery_notes = form.can_edit_delivery_notes.data
        user.can_print_reports = form.can_print_reports.data
        
        db.session.add(user)
        db.session.commit()
        flash('Utilisateur créé avec succès!', 'success')
        return redirect(url_for('users.index'))
    
    return render_template('users/create.html', form=form)

@bp.route('/<int:id>')
def show(id):
    user = User.query.get_or_404(id)
    return render_template('users/show.html', user=user)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
def edit(id):
    user = User.query.get_or_404(id)
    form = EditUserForm(original_username=user.username, original_email=user.email)
    
    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.full_name = form.full_name.data
        user.is_admin = form.is_admin.data
        
        if form.password.data:
            user.set_password(form.password.data)
        
        # Update permissions
        user.can_view_products = form.can_view_products.data
        user.can_edit_products = form.can_edit_products.data
        user.can_view_stock = form.can_view_stock.data
        user.can_edit_stock = form.can_edit_stock.data
        user.can_view_clients = form.can_view_clients.data
        user.can_edit_clients = form.can_edit_clients.data
        user.can_view_quotes = form.can_view_quotes.data
        user.can_edit_quotes = form.can_edit_quotes.data
        user.can_view_invoices = form.can_view_invoices.data
        user.can_edit_invoices = form.can_edit_invoices.data
        user.can_view_delivery_notes = form.can_view_delivery_notes.data
        user.can_edit_delivery_notes = form.can_edit_delivery_notes.data
        user.can_print_reports = form.can_print_reports.data
        
        user.updated_at = datetime.utcnow()
        
        db.session.commit()
        flash('Utilisateur mis à jour avec succès!', 'success')
        return redirect(url_for('users.show', id=user.id))
    
    elif request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.full_name.data = user.full_name
        form.is_admin.data = user.is_admin
        
        # Set permissions
        form.can_view_products.data = user.can_view_products
        form.can_edit_products.data = user.can_edit_products
        form.can_view_stock.data = user.can_view_stock
        form.can_edit_stock.data = user.can_edit_stock
        form.can_view_clients.data = user.can_view_clients
        form.can_edit_clients.data = user.can_edit_clients
        form.can_view_quotes.data = user.can_view_quotes
        form.can_edit_quotes.data = user.can_edit_quotes
        form.can_view_invoices.data = user.can_view_invoices
        form.can_edit_invoices.data = user.can_edit_invoices
        form.can_view_delivery_notes.data = user.can_view_delivery_notes
        form.can_edit_delivery_notes.data = user.can_edit_delivery_notes
        form.can_print_reports.data = user.can_print_reports
    
    return render_template('users/edit.html', form=form, user=user)

@bp.route('/<int:id>/delete', methods=['POST'])
def delete(id):
    user = User.query.get_or_404(id)
    db.session.delete(user)
    db.session.commit()
    flash('Utilisateur supprimé avec succès!', 'success')
    return redirect(url_for('users.index'))

@bp.route('/report')
def report():
    users = User.query.all()
    return render_template('users/report.html', users=users)
