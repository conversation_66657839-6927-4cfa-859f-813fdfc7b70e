{% extends 'base.html' %}

{% block title %}Retour au Stock - Gestion d'Extincteurs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="display-6">
                        <i class="fas fa-undo-alt me-2 text-success"></i>Retour au Stock
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Accueil</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('main.products') }}">Produits & Stock</a></li>
                            <li class="breadcrumb-item active">Retour au Stock</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ url_for('main.products') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Retour
                    </a>
                </div>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-undo-alt me-2"></i>Enregistrer un Retour de Produit
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Retour au stock :</strong> Utilisez cette fonction pour enregistrer le retour de produits 
                                de la part des clients (retours, échanges, produits non utilisés, etc.)
                            </div>

                            <form method="POST">
                                {{ form.hidden_tag() }}
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_id" class="form-label required">
                                                <i class="fas fa-box me-1"></i>Produit
                                            </label>
                                            {{ form.product_id(class="form-select") }}
                                            {% if form.product_id.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.product_id.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="quantity" class="form-label required">
                                                <i class="fas fa-plus-circle me-1"></i>Quantité à retourner
                                            </label>
                                            {{ form.quantity(class="form-control", min="1", placeholder="Ex: 5") }}
                                            {% if form.quantity.errors %}
                                                <div class="text-danger">
                                                    {% for error in form.quantity.errors %}
                                                        <small>{{ error }}</small>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="reference_document" class="form-label">
                                        <i class="fas fa-file-alt me-1"></i>Référence Document (optionnel)
                                    </label>
                                    {{ form.reference_document(class="form-control", placeholder="Ex: BON-RET-2024-001, Facture #123") }}
                                    {% if form.reference_document.errors %}
                                        <div class="text-danger">
                                            {% for error in form.reference_document.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Numéro de bon de retour, facture d'origine, etc.</div>
                                </div>

                                <div class="mb-4">
                                    <label for="notes" class="form-label">
                                        <i class="fas fa-sticky-note me-1"></i>Notes (optionnel)
                                    </label>
                                    {{ form.notes(class="form-control", rows="4", placeholder="Raison du retour, état du produit, observations...") }}
                                    {% if form.notes.errors %}
                                        <div class="text-danger">
                                            {% for error in form.notes.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Précisez la raison du retour, l'état du produit, etc.</div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ url_for('main.products') }}" class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i>Annuler
                                    </a>
                                    {{ form.submit(class="btn btn-success", value="Enregistrer le Retour") }}
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Informations supplémentaires -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body text-center">
                                    <i class="fas fa-info-circle fa-2x text-info mb-2"></i>
                                    <h6 class="card-title">Types de Retours</h6>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-check text-success me-1"></i> Retours clients</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Échanges</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Produits non utilisés</li>
                                        <li><i class="fas fa-check text-success me-1"></i> Retours de garantie</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                    <h6 class="card-title">Important</h6>
                                    <ul class="list-unstyled text-start">
                                        <li><i class="fas fa-arrow-right text-warning me-1"></i> Vérifiez l'état du produit</li>
                                        <li><i class="fas fa-arrow-right text-warning me-1"></i> Documentez la raison</li>
                                        <li><i class="fas fa-arrow-right text-warning me-1"></i> Gardez les justificatifs</li>
                                        <li><i class="fas fa-arrow-right text-warning me-1"></i> Informez le client</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on product selection
    const productSelect = document.querySelector('select[name="product_id"]');
    if (productSelect) {
        productSelect.focus();
    }

    // Form validation
    const form = document.querySelector('form');
    const quantityInput = document.querySelector('input[name="quantity"]');
    
    form.addEventListener('submit', function(e) {
        const quantity = parseInt(quantityInput.value);
        
        if (!quantity || quantity <= 0) {
            e.preventDefault();
            alert('Veuillez saisir une quantité valide (supérieure à 0).');
            quantityInput.focus();
            return false;
        }
        
        // Confirmation
        const productSelect = document.querySelector('select[name="product_id"]');
        const productName = productSelect.options[productSelect.selectedIndex].text;
        
        if (!confirm(`Confirmer le retour de ${quantity} unité(s) de "${productName}" au stock ?`)) {
            e.preventDefault();
            return false;
        }
    });
    
    // Auto-calculate and show current stock when product is selected
    const productSelect = document.querySelector('select[name="product_id"]');
    if (productSelect) {
        productSelect.addEventListener('change', function() {
            if (this.value) {
                // Extract current stock from option text (if available)
                const optionText = this.options[this.selectedIndex].text;
                console.log('Produit sélectionné:', optionText);
            }
        });
    }
});
</script>
{% endblock %}
