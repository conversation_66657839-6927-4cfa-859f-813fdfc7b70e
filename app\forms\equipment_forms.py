from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, IntegerField, DateField, SelectField, TextAreaField, SubmitField, FieldList, FormField
from wtforms.validators import DataRequired, NumberRange, Optional
from datetime import datetime, timedelta

class EquipmentItemForm(FlaskForm):
    """Formulaire pour un élément d'équipement (désignation + quantité)"""
    designation = StringField('Désignation', validators=[DataRequired()],
                             render_kw={"placeholder": "Ex: Extincteur CO2 5kg"})
    quantity = IntegerField('Quantité', validators=[DataRequired(), NumberRange(min=1)],
                           default=1, render_kw={"min": "1"})

class EquipmentMaintenanceForm(FlaskForm):
    # Informations du client
    client_id = SelectField('Client', validators=[DataRequired()], coerce=int,
                           render_kw={"class": "form-control"})

    # Liste des équipements (désignation + quantité)
    equipment_items = FieldList(FormField(EquipmentItemForm), min_entries=1)

    supply_date = DateField('Date de fourniture', validators=[DataRequired()],
                           default=datetime.now().date())

    verification_date = DateField('Date de vérification', validators=[Optional()])

    current_situation = SelectField('Situation actuelle',
                                   choices=[
                                       ('garantie', 'Garantie'),
                                       ('recharge', 'Recharge'),
                                       ('verification', 'Vérification'),
                                       ('changement', 'Changement')
                                   ],
                                   validators=[DataRequired()],
                                   default='garantie')

    # Dates de fin selon la situation
    warranty_end_date = DateField('Date de fin de garantie', validators=[Optional()])
    recharge_end_date = DateField('Date de fin de recharge', validators=[Optional()])
    verification_end_date = DateField('Date de fin de vérification', validators=[Optional()])
    replacement_end_date = DateField('Date de fin de changement', validators=[Optional()])

    notes = TextAreaField('Notes', validators=[Optional()],
                         render_kw={"rows": "3", "placeholder": "Notes ou observations..."})

    submit = SubmitField('Enregistrer')

    def __init__(self, *args, **kwargs):
        super(EquipmentMaintenanceForm, self).__init__(*args, **kwargs)

        # Load clients for the dropdown
        from app.models import Client
        clients = Client.query.order_by(Client.name).all()
        self.client_id.choices = [(0, 'Sélectionner un client')] + [(c.id, c.name) for c in clients]

        # Set default end dates based on supply date
        if self.supply_date.data:
            supply_date = self.supply_date.data
            # Default warranty: 2 years
            if not self.warranty_end_date.data:
                self.warranty_end_date.data = supply_date + timedelta(days=730)
            # Default recharge: 1 year
            if not self.recharge_end_date.data:
                self.recharge_end_date.data = supply_date + timedelta(days=365)
            # Default verification: 1 year
            if not self.verification_end_date.data:
                self.verification_end_date.data = supply_date + timedelta(days=365)
            # Default replacement: 10 years
            if not self.replacement_end_date.data:
                self.replacement_end_date.data = supply_date + timedelta(days=3650)


class EquipmentEditForm(FlaskForm):
    """Formulaire pour modifier un équipement individuel"""
    client_id = SelectField('Client', validators=[DataRequired()], coerce=int,
                           render_kw={"class": "form-control"})

    designation = StringField('Désignation', validators=[DataRequired()],
                             render_kw={"placeholder": "Ex: Extincteur CO2 5kg"})
    quantity = IntegerField('Quantité', validators=[DataRequired(), NumberRange(min=1)],
                           default=1, render_kw={"min": "1"})

    supply_date = DateField('Date de fourniture', validators=[DataRequired()],
                           default=datetime.now().date())

    verification_date = DateField('Date de vérification', validators=[Optional()])

    current_situation = SelectField('Situation actuelle',
                                   choices=[
                                       ('garantie', 'Garantie'),
                                       ('recharge', 'Recharge'),
                                       ('verification', 'Vérification'),
                                       ('changement', 'Changement')
                                   ],
                                   validators=[DataRequired()],
                                   default='garantie')

    # Dates de fin selon la situation
    warranty_end_date = DateField('Date de fin de garantie', validators=[Optional()])
    recharge_end_date = DateField('Date de fin de recharge', validators=[Optional()])
    verification_end_date = DateField('Date de fin de vérification', validators=[Optional()])
    replacement_end_date = DateField('Date de fin de changement', validators=[Optional()])

    notes = TextAreaField('Notes', validators=[Optional()],
                         render_kw={"rows": "3", "placeholder": "Notes ou observations..."})

    submit = SubmitField('Modifier')

    def __init__(self, *args, **kwargs):
        super(EquipmentEditForm, self).__init__(*args, **kwargs)

        # Load clients for the dropdown
        from app.models import Client
        clients = Client.query.order_by(Client.name).all()
        self.client_id.choices = [(c.id, c.name) for c in clients]


class EquipmentSearchForm(FlaskForm):
    search = StringField('Rechercher', render_kw={"placeholder": "Rechercher par désignation..."})
    situation_filter = SelectField('Filtrer par situation',
                                  choices=[
                                      ('', 'Toutes les situations'),
                                      ('garantie', 'Garantie'),
                                      ('recharge', 'Recharge'),
                                      ('verification', 'Vérification'),
                                      ('changement', 'Changement')
                                  ])
    alert_filter = SelectField('Filtrer par alerte',
                              choices=[
                                  ('', 'Tous'),
                                  ('expired', 'Expirés'),
                                  ('alert', 'Alertes (≤10 jours)'),
                                  ('ok', 'OK (>10 jours)')
                              ])
    submit = SubmitField('Filtrer')
