{% extends "base.html" %}

{% block title %}Journaux d'Activité{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-list text-primary me-2"></i>
                    Journaux d'Activité
                </h1>
                <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Retour au Tableau de Bord
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>Filtres
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('admin.activity_logs') }}">
                <div class="row">
                    <div class="col-md-2 mb-3">
                        <label for="user" class="form-label">Utilisateur</label>
                        <select name="user" id="user" class="form-select">
                            <option value="">Tous les utilisateurs</option>
                            {% for user in unique_users %}
                            <option value="{{ user }}" {% if filters.user == user %}selected{% endif %}>
                                {{ user }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="action" class="form-label">Action</label>
                        <select name="action" id="action" class="form-select">
                            <option value="">Toutes les actions</option>
                            {% for action in unique_actions %}
                            <option value="{{ action }}" {% if filters.action == action %}selected{% endif %}>
                                {{ action }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="module" class="form-label">Module</label>
                        <select name="module" id="module" class="form-select">
                            <option value="">Tous les modules</option>
                            {% for module in unique_modules %}
                            <option value="{{ module }}" {% if filters.module == module %}selected{% endif %}>
                                {{ module }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">Date de début</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" 
                               value="{{ filters.date_from or '' }}">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_to" class="form-label">Date de fin</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" 
                               value="{{ filters.date_to or '' }}">
                    </div>
                    <div class="col-md-2 mb-3 d-flex align-items-end">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Filtrer
                            </button>
                            <a href="{{ url_for('admin.activity_logs') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Activity Logs Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-history me-2"></i>
                Activités ({{ activities.total }} résultats)
            </h6>
        </div>
        <div class="card-body">
            {% if activities.items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Date/Heure</th>
                                <th>Utilisateur</th>
                                <th>Action</th>
                                <th>Module</th>
                                <th>Description</th>
                                <th>IP</th>
                                <th>Détails</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for activity in activities.items %}
                            <tr>
                                <td>
                                    <small class="text-muted">
                                        {{ activity.timestamp.strftime('%d/%m/%Y') }}<br>
                                        {{ activity.timestamp.strftime('%H:%M:%S') }}
                                    </small>
                                </td>
                                <td>
                                    <strong>{{ activity.username }}</strong>
                                    {% if activity.user %}
                                        <br><small class="text-muted">ID: {{ activity.user_id }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ activity.action_color }}">
                                        <i class="{{ activity.action_icon }}"></i>
                                        {{ activity.action }}
                                    </span>
                                </td>
                                <td>
                                    {% if activity.module %}
                                        <span class="badge bg-secondary">{{ activity.module }}</span>
                                        {% if activity.record_id %}
                                            <br><small class="text-muted">ID: {{ activity.record_id }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="{{ activity.description or '' }}">
                                        {{ activity.description or '-' }}
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">{{ activity.ip_address or '-' }}</small>
                                </td>
                                <td>
                                    {% if activity.old_values or activity.new_values %}
                                        <button class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#detailsModal{{ activity.id }}">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if activities.pages > 1 %}
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if activities.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.activity_logs', page=activities.prev_num, **filters) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in activities.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != activities.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.activity_logs', page=page_num, **filters) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if activities.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.activity_logs', page=activities.next_num, **filters) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune activité trouvée</h5>
                    <p class="text-muted">Essayez de modifier vos critères de recherche</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Details Modals -->
{% for activity in activities.items %}
    {% if activity.old_values or activity.new_values %}
    <div class="modal fade" id="detailsModal{{ activity.id }}" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="{{ activity.action_icon }} me-2"></i>
                        Détails de l'Activité
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">Informations Générales</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Utilisateur:</strong></td>
                                    <td>{{ activity.username }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Action:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ activity.action_color }}">
                                            {{ activity.action }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Date/Heure:</strong></td>
                                    <td>{{ activity.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Adresse IP:</strong></td>
                                    <td>{{ activity.ip_address or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">Navigateur</h6>
                            <small class="text-muted">
                                {{ activity.user_agent or '-' }}
                            </small>
                        </div>
                    </div>
                    
                    {% if activity.old_values %}
                    <div class="mt-3">
                        <h6 class="text-muted">Anciennes Valeurs</h6>
                        <pre class="bg-light p-2 rounded"><code>{{ activity.old_values }}</code></pre>
                    </div>
                    {% endif %}
                    
                    {% if activity.new_values %}
                    <div class="mt-3">
                        <h6 class="text-muted">Nouvelles Valeurs</h6>
                        <pre class="bg-light p-2 rounded"><code>{{ activity.new_values }}</code></pre>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
{% endfor %}
{% endblock %}
