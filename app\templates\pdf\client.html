<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fiche Client: {{ client.name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            margin-bottom: 30px;
            text-align: center;
        }
        .logo {
            color: #dc3545;
            font-size: 32px;
            font-weight: bold;
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo-icon {
            width: 50px;
            height: 50px;
            background-color: #dc3545;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .logo-icon span {
            color: white;
            font-size: 24px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-top: 20px;
            text-align: center;
            color: #dc3545;
        }
        .client-info {
            border: 1px solid #ccc;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .info-row {
            margin-bottom: 15px;
            display: flex;
        }
        .info-label {
            font-weight: bold;
            width: 150px;
        }
        .info-value {
            border: 1px solid #ccc;
            padding: 5px;
            width: 300px;
        }
        .footer {
            font-size: 10px;
            text-align: center;
            margin-top: 20px;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        @media print {
            body {
                padding: 0;
                margin: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        {% if (company and company.logo) or (global_company and global_company.logo) %}
        <div class="logo">
            <img src="{{ url_for('static', filename=(company.logo if company else global_company.logo), _external=True) }}" alt="{{ (company.name if company else global_company.name) }}" style="max-height: 80px;">
        </div>
        {% else %}
        <div class="logo">
            <div class="logo-icon"><span>M</span></div>
            {{ (company.name if company else global_company.name) if (company or global_company) else "MAX AFFAIRE" }}
        </div>
        {% endif %}
        <div class="title">FICHE CLIENT</div>
    </div>

    <div class="client-info">
        <div class="info-row">
            <div class="info-label">ID:</div>
            <div class="info-value">{{ client.id }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Nom:</div>
            <div class="info-value">{{ client.name }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Adresse:</div>
            <div class="info-value">{{ client.address }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Email:</div>
            <div class="info-value">{{ client.email }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">Téléphone:</div>
            <div class="info-value">{{ client.phone }}</div>
        </div>
        <div class="info-row">
            <div class="info-label">ICE:</div>
            <div class="info-value">{{ client.ice or 'Non spécifié' }}</div>
        </div>
    </div>

    <div class="footer">
        {% set comp = company if company else global_company %}
        {% if comp %}
        <p>
            {{ comp.legal_form if comp.legal_form }} au capital de {{ comp.capital if comp.capital else "N/A" }} -
            adresse : {{ comp.address if comp.address else "N/A" }} /
            RC N° : {{ comp.rc_number if comp.rc_number else "N/A" }} -
            Patente N° {{ comp.patent_number if comp.patent_number else "N/A" }}
            IF : {{ comp.tax_id if comp.tax_id else "N/A" }} -
            CNSS N° : {{ comp.cnss_number if comp.cnss_number else "N/A" }}
        </p>
        <p>
            Tél: {{ comp.phone if comp.phone else "N/A" }}
            {% if comp.fax %}Fax: {{ comp.fax }}{% endif %}
            Email: {{ comp.email if comp.email else "N/A" }} -
            ICE: {{ comp.ice_number if comp.ice_number else "N/A" }}
        </p>
        {% else %}
        <p>
            SARL au capital de 2 110 000,00 dh - adresse : N° 19, Amal 14 CYM Hay Elkhier Rabat /RC N° : 64133 - Patente N° 27395222 IF :3346362 - CNSS N° : 7304405
        </p>
        <p>
            Tél: 05 37 29 50 31 Fax: 05 37 69 20 86 Email: <EMAIL> - ICE: 001560529000092
        </p>
        {% endif %}
    </div>

    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()">Imprimer</button>
        <button onclick="window.close()">Fermer</button>
    </div>
</body>
</html>
