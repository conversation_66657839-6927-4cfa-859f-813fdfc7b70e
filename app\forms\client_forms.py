from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Length, Email, Optional

class ClientForm(FlaskForm):
    name = String<PERSON>ield('Nom du client', validators=[DataRequired(), Length(min=2, max=100)])
    address = TextAreaField('Adresse', validators=[Optional(), Length(max=200)])
    city = StringField('Ville', validators=[Optional(), Length(max=50)])
    postal_code = StringField('Code postal', validators=[Optional(), Length(max=20)])
    email = StringField('Email', validators=[Optional(), Email(), Length(max=120)])
    phone = StringField('Téléphone', validators=[Optional(), Length(max=20)])
    ice = StringField('ICE', validators=[Optional(), Length(max=50)])
    submit = SubmitField('Enregistrer')
